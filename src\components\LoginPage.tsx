import { SignInForm } from "../SignInForm";
import { ParticlesBackground } from "./ParticlesBackground";
import { ArrowR<PERSON>, Shield, Zap, Globe, Sparkles } from "lucide-react";

export function LoginPage() {
  return (
    <div className="dark bg-black text-gray-300 antialiased min-h-screen w-full relative overflow-hidden">
      {/* Sophisticated Hero Background Effects */}
      <div className="absolute inset-0 bg-black">
        {/* Premium Central Gradient */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[1000px] bg-gradient-to-br from-zinc-800/20 via-slate-800/15 to-gray-800/20 rounded-full blur-[400px] animate-pulse-slow opacity-80" />
        
        {/* Elegant Floating Orbs */}
        <div className="absolute top-16 left-16 w-80 h-80 bg-gradient-to-br from-zinc-700/25 to-transparent rounded-full blur-3xl animate-float opacity-60" />
        <div className="absolute bottom-16 right-16 w-96 h-96 bg-gradient-to-tl from-slate-700/30 to-transparent rounded-full blur-3xl animate-float animation-delay-3000 opacity-70" />
        <div className="absolute top-32 right-1/3 w-64 h-64 bg-gradient-to-bl from-gray-700/20 to-transparent rounded-full blur-2xl animate-float animation-delay-1500 opacity-50" />
        <div className="absolute bottom-32 left-1/3 w-72 h-72 bg-gradient-to-tr from-zinc-600/25 to-transparent rounded-full blur-3xl animate-float animation-delay-4000 opacity-65" />
        
        {/* Subtle Geometric Elements */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-zinc-400/60 rounded-full animate-pulse opacity-80" />
        <div className="absolute top-2/3 right-1/4 w-1.5 h-1.5 bg-slate-400/50 rounded-full animate-pulse animation-delay-2000 opacity-70" />
        <div className="absolute bottom-1/4 left-2/3 w-2.5 h-2.5 bg-gray-400/40 rounded-full animate-pulse animation-delay-1000 opacity-60" />
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-zinc-300/70 rounded-full animate-pulse animation-delay-3500 opacity-50" />
        
        {/* Refined Ring Effects */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] border border-zinc-700/10 rounded-full animate-spin-slow" />
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] border border-slate-700/8 rounded-full animate-spin-slow animation-delay-2000" style={{animationDirection: 'reverse'}} />
        
        {/* Premium Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.008)_1px,transparent_1px)] bg-[size:100px_100px] opacity-40" />
        
        {/* Sophisticated Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
        <div className="absolute inset-0 bg-gradient-to-b from-zinc-900/5 via-transparent to-slate-900/5" />
      </div>

      {/* Premium Border Effects */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-zinc-500/30 to-transparent" />
      <div className="absolute top-0 left-1/2 w-[150%] h-px -translate-x-1/2 bg-gradient-to-r from-transparent via-zinc-300/20 to-transparent animate-aurora-border" />

      {/* Header */}
      <header className="sticky top-0 z-50 bg-black/30 backdrop-blur-lg border-b border-white/10">
        <div className="container mx-auto px-6 h-16 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img src="/icon0.svg" alt="ErzenAI logo" className="h-8 w-8 rounded-lg" />
            <span className="text-2xl font-bold text-white">ErzenAI</span>
          </div>
          <div className="text-sm text-zinc-400">
            Back to{" "}
            <a href="/" className="text-white hover:text-zinc-200 transition-colors font-medium">
              Homepage
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex flex-col min-h-[calc(100vh-4rem)]">
        <div className="flex-1 flex items-center justify-center px-6 py-8">
          <div className="w-full max-w-5xl mx-auto grid lg:grid-cols-2 gap-8 items-center">
            
            {/* Left Side - Welcome Content */}
            <div className="space-y-6 text-center lg:text-left">
              {/* Status Badge */}
              <div className="flex justify-center lg:justify-start">
                <div className="flex items-center space-x-2 bg-zinc-900/50 backdrop-blur-sm border border-zinc-800/50 rounded-full px-3 py-1.5">
                  <div className="w-2 h-2 bg-emerald-400/80 rounded-full animate-pulse" />
                  <span className="text-xs text-zinc-400 font-medium tracking-wide uppercase">AI-Powered Workspace</span>
                </div>
              </div>

              {/* Main Heading */}
              <div className="space-y-4">
                <h1 className="text-4xl sm:text-5xl font-black tracking-[-0.02em] text-white leading-tight animate-fade-in">
                  <span className="bg-gradient-to-br from-white via-zinc-100 to-zinc-300 bg-clip-text text-transparent">
                    Welcome Back to
                  </span>
                  <br />
                  <span className="bg-gradient-to-br from-zinc-200 via-zinc-300 to-zinc-400 bg-clip-text text-transparent">
                    ErzenAI
                  </span>
                </h1>
                
                <p className="text-lg text-zinc-300 leading-relaxed animate-fade-in animation-delay-300 font-light max-w-xl">
                  Continue your AI-powered journey. Access 75+ models, instant streaming responses, and autonomous execution.
                </p>
              </div>

              {/* Simple feature highlight */}
              <div className="text-center lg:text-left animate-fade-in animation-delay-600">
                <p className="text-sm text-zinc-500 mb-2">Trusted by innovators at</p>
                <div className="flex flex-wrap justify-center lg:justify-start gap-4 text-zinc-400 text-sm">
                  <span>Microsoft</span>
                  <span>•</span>
                  <span>Google</span>
                  <span>•</span>
                  <span>Meta</span>
                  <span>•</span>
                  <span>OpenAI</span>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="relative animate-fade-in animation-delay-300">
              {/* Form Container with Enhanced Styling */}
              <div className="relative group">
                {/* Glow Effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/20 to-zinc-600/0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
                
                {/* Main Form Card */}
                <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/40 to-zinc-900/60 backdrop-blur-xl border border-zinc-800/60 rounded-3xl p-6 md:p-8 transition-all duration-300 hover:border-zinc-700/80 shadow-2xl">
                  {/* Subtle Pattern Overlay */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-4 right-4 w-12 h-12 border border-white/20 rounded-full" />
                    <div className="absolute bottom-6 left-6 w-8 h-8 border border-white/10 rounded-full" />
                    <div className="absolute top-1/2 right-6 w-6 h-6 border border-white/15 rotate-45" />
                  </div>

                  <div className="relative z-10 space-y-6">
                    {/* Form Header */}
                    <div className="text-center space-y-3">
                      <div className="flex justify-center mb-3">
                        <div className="w-2 h-2 bg-zinc-400/60 rounded-full animate-pulse" />
                      </div>
                      
                      <h2 className="text-2xl md:text-3xl font-bold text-white tracking-tight">
                        Sign In
                      </h2>
                      
                      <p className="text-zinc-400">
                        Continue to your AI workspace
                      </p>
                    </div>

                    {/* Sign In Form */}
                    <div>
                      <SignInForm />
                    </div>

                    {/* Additional Info */}
                    <div className="text-center pt-4 border-t border-zinc-800/50">
                      <p className="text-sm text-zinc-500 leading-relaxed">
                        New to ErzenAI?{" "}
                        <a href="/" className="text-blue-400 hover:text-blue-300 transition-colors font-medium">
                          Explore features
                        </a>
                        {" "}or{" "}
                        <span className="text-white font-medium cursor-pointer hover:text-zinc-200 transition-colors">
                          start your free trial
                        </span>
                      </p>
                    </div>

                    {/* Security Notice */}
                    <div className="flex items-center justify-center space-x-2 text-xs text-zinc-600">
                      <Shield className="h-3 w-3" />
                      <span>Enterprise-grade security & privacy</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="relative mt-auto py-6">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="text-sm text-zinc-600">
                © 2025 ErzenAI. All rights reserved.
              </div>
              <div className="flex items-center space-x-6 text-sm text-zinc-600">
                <a href="/privacy" className="hover:text-zinc-400 transition-colors">Privacy</a>
                <a href="/terms" className="hover:text-zinc-400 transition-colors">Terms</a>
                <a href="/support" className="hover:text-zinc-400 transition-colors">Support</a>
              </div>
            </div>
          </div>
        </footer>
      </main>
    </div>
  );
} 