import React, { useState, useRef, useEffect, useMemo } from "react";
import { createPortal } from "react-dom";
import {
  ChevronDown,
  MoreHorizontal,
  Sparkles,
  Lightbulb,
  Brain,
  Bot,
  User,
  Heart,
  Users,
  Smile,
  Stethoscope,
  MessageSquareHeart,
  MessageSquare,
  ListCollapse,
  Mail,
  Code,
  BookOpen,
  Search,
  Wrench,
  Calculator,
  Clock,
  CloudRain,
  Zap,
  Link,
  Database,
  Camera,
  Binoculars,
  Check,
  Settings,
  Settings2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { PERSONAS } from "@/lib/personas";
import { RECIPES } from "@/lib/recipes";

// A simple hook to check for desktop screen sizes
const useIsDesktop = () => {
  const [isDesktop, setIsDesktop] = useState(window.innerWidth >= 768)
  useEffect(() => {
    const handleResize = () => setIsDesktop(window.innerWidth >= 768)
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])
  return isDesktop
}

interface OptionsSelectorProps {
  // Personas & Recipes
  persona: string;
  recipe: string;
  onPersonaChange: (persona: string) => void;
  onRecipeChange: (recipe: string) => void;
  
  // Tools
  enabledTools: string[];
  availableTools: any[];
  modelSupportsTools: boolean;
  modelDisplayName: string;
  onToggleTool: (toolId: string) => void;
  
  // Thinking
  thinkingBudget?: string | number;
  modelThinkingBudgets?: (string | number)[];
  onThinkingBudgetChange?: (budget: string | number) => void;
  
  // State
  isCustomToolActive: boolean;
  disabled?: boolean;
}

// Helper function to get thinking budget label
const getThinkingBudgetLabel = (budget: string | number): string => {
  if (typeof budget === "number" || !isNaN(Number(budget))) {
    const numBudget = Number(budget);
    if (numBudget === 0) return "None";
    if (numBudget <= 3000) return "Low";
    if (numBudget <= 8000) return "Medium";
    if (numBudget <= 15000) return "High";
    if (numBudget <= 20000) return "Very High";
    return "Maximum";
  }
  // String budgets
  switch (budget) {
    case "low": return "Low";
    case "medium": return "Medium";
    case "high": return "High";
    default: return String(budget);
  }
};

export function OptionsSelector({
  persona,
  recipe,
  onPersonaChange,
  onRecipeChange,
  enabledTools,
  availableTools,
  modelSupportsTools,
  modelDisplayName,
  onToggleTool,
  thinkingBudget,
  modelThinkingBudgets = [],
  onThinkingBudgetChange,
  isCustomToolActive,
  disabled,
}: OptionsSelectorProps) {
  const [showSelector, setShowSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<"style" | "tools" | "thinking">("style");
  const [searchQuery, setSearchQuery] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState<
    { bottom: number; left: number; width: number } | null
  >(null);
  
  const selectorRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const isDesktop = useIsDesktop();

  // Categorize tools
  const categorizedTools = useMemo(() => {
    const tools = { regular: [] as any[], mcp: [] as any[], n8n: [] as any[] };
    availableTools.forEach((tool) => {
      const id: string = tool.id || "";
      if (id.startsWith("mcp_")) tools.mcp.push(tool);
      else if (id.startsWith("n8n_")) {
        const segments = id.split("_");
        if (segments.length === 2) tools.n8n.push(tool);
      } else tools.regular.push(tool);
    });
    return tools;
  }, [availableTools]);

  // Filter tools based on search
  const getFilteredTools = (tools: any[]) => {
    if (!searchQuery) return tools;
    return tools.filter(tool => 
      tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.id.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  // Handle outside clicks
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const isClickInsideButton = selectorRef.current?.contains(target);
      const isClickInsideDropdown = dropdownRef.current?.contains(target);
      if (!isClickInsideButton && !isClickInsideDropdown) {
        setShowSelector(false);
        setSearchQuery("");
        setDropdownPosition(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleToggleSelector = () => {
    if (disabled) return;

    if (showSelector) {
      setShowSelector(false);
    } else {
      if (isDesktop && selectorRef.current) {
        const rect = selectorRef.current.getBoundingClientRect();
        
        const SIDE_MARGIN = 8;
        const MAX_WIDTH = 440;
        const dropdownWidth = Math.min(MAX_WIDTH, window.innerWidth - SIDE_MARGIN * 2);

        const clampedLeft = Math.min(
          Math.max(SIDE_MARGIN, rect.left),
          window.innerWidth - dropdownWidth - SIDE_MARGIN,
        );

        setDropdownPosition({
          bottom: window.innerHeight - rect.top + SIDE_MARGIN,
          left: clampedLeft,
          width: dropdownWidth,
        });
      }
      setShowSelector(true);
    }
  };

  // Count active options
  const activeCount = 
    (persona && persona !== "none" ? 1 : 0) +
    (recipe && recipe !== "none" ? 1 : 0) +
    enabledTools.length +
    (thinkingBudget && thinkingBudget !== 0 ? 1 : 0);

  const renderPersonaIcon = (personaId: string) => {
    const Icon = ({
      none: User,
      companion: Heart,
      friend: Users,
      comedian: Smile,
      not_a_doctor: Stethoscope,
      not_a_therapist: MessageSquareHeart,
    } as const)[personaId] ?? Bot;
    return <Icon size={14} />;
  };

  const renderRecipeIcon = (recipeId: string) => {
    const Icon = ({
      none: MessageSquare,
      summarise: ListCollapse,
      brainstorm: Lightbulb,
      email_draft: Mail,
      code_review: Code,
      learning_plan: BookOpen,
      problem_solver: Brain,
    } as const)[recipeId] ?? Bot;
    return <Icon size={14} />;
  };

  const renderToolIcon = (toolId: string) => {
    if (toolId.startsWith("mcp_")) return <Bot size={14} />;
    switch (toolId) {
      case "web_search": return <Search size={14} />;
      case "deep_search": return <Binoculars size={14} />;
      case "read_url": return <Link size={14} />;
      case "calculator": return <Calculator size={14} />;
      case "image_generation": return <Camera size={14} />;
      case "weather": return <CloudRain size={14} />;
      case "get_time": return <Clock size={14} />;
      case "code_search": return <Code size={14} />;
      case "database_query": return <Database size={14} />;
      default: return <Wrench size={14} />;
    }
  };

  const showToolsTab = !isCustomToolActive && modelSupportsTools && availableTools.length > 0;
  const showThinkingTab = !isCustomToolActive && modelThinkingBudgets && modelThinkingBudgets.length > 0;

  return (
    <div className="relative" ref={selectorRef}>
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={handleToggleSelector}
              disabled={disabled}
              className={cn(
                "h-9 w-9 rounded-lg",
                activeCount > 0 ? "text-foreground" : "text-muted-foreground",
                "hover:bg-muted/50 hover:text-foreground focus-visible:ring-0",
                disabled && "opacity-50 cursor-not-allowed",
              )}
            >
              <Settings2 size={16} />
              {activeCount > 0 && (
                <Badge 
                  variant="secondary" 
                  className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-[10px]"
                >
                  {activeCount}
                </Badge>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            {activeCount > 0 ? `${activeCount} option${activeCount > 1 ? 's' : ''} active` : "Options"}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {showSelector && !isDesktop && createPortal(
        <div className="fixed inset-0 bg-black/40 backdrop-blur-sm z-[9998]" onClick={() => setShowSelector(false)} />,
        document.body,
      )}

      {showSelector && !disabled && createPortal(
        <div
          ref={dropdownRef}
          style={
            isDesktop && dropdownPosition
              ? {
                  position: "fixed",
                  bottom: `${dropdownPosition.bottom}px`,
                  left: `${dropdownPosition.left}px`,
                  width: `${dropdownPosition.width}px`,
                }
              : {}
          }
          className={cn(
            "bg-popover border rounded-xl shadow-2xl z-[9999] overflow-hidden flex flex-col backdrop-blur-md",
            isDesktop
              ? "max-h-[55vh] w-full max-w-[440px]"
              : "fixed bottom-0 left-0 right-0 rounded-b-none w-full max-h-[75vh] animate-in slide-in-from-bottom-2 duration-200",
          )}
        >
          {/* Mobile drag handle */}
          {!isDesktop && (
            <div className="flex justify-center pt-2 pb-1">
              <div className="w-12 h-1 bg-muted-foreground/30 rounded-full" />
            </div>
          )}
          
          {/* Header with tabs */}
          <div className="border-b border-border/50">
            <div className="flex text-xs sm:text-sm">
              <button
                onClick={() => setActiveTab("style")}
                className={cn(
                  "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all flex items-center justify-center gap-1.5 sm:gap-2",
                  activeTab === "style"
                    ? "bg-primary/10 text-primary border-b-2 border-primary"
                    : "text-muted-foreground hover:text-foreground hover:bg-muted/30",
                )}
              >
                <Sparkles size={12} className="sm:h-[14px] sm:w-[14px]" />
                Style
              </button>
              {showToolsTab && (
                <button
                  onClick={() => setActiveTab("tools")}
                  className={cn(
                    "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all flex items-center justify-center gap-1.5 sm:gap-2",
                    activeTab === "tools"
                      ? "bg-primary/10 text-primary border-b-2 border-primary"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                >
                  <Wrench size={12} className="sm:h-[14px] sm:w-[14px]" />
                  Tools
                  {enabledTools.length > 0 && (
                    <Badge variant="secondary" className="ml-1 h-5 px-1.5 text-[10px]">
                      {enabledTools.length}
                    </Badge>
                  )}
                </button>
              )}
              {showThinkingTab && (
                <button
                  onClick={() => setActiveTab("thinking")}
                  className={cn(
                    "flex-1 px-3 sm:px-4 py-2.5 sm:py-3 font-medium transition-all flex items-center justify-center gap-1.5 sm:gap-2",
                    activeTab === "thinking"
                      ? "bg-primary/10 text-primary border-b-2 border-primary"
                      : "text-muted-foreground hover:text-foreground",
                  )}
                >
                  <Brain size={12} className="sm:h-[14px] sm:w-[14px]" />
                  Thinking
                </button>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
            {/* Style Tab - Personas & Recipes */}
            {activeTab === "style" && (
              <div className="p-3 sm:p-4">
                {/* Personas Section */}
                <div className="mb-4">
                  <div className="text-[10px] sm:text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 px-2">
                    Persona
                  </div>
                  <div className="space-y-1">
                    {PERSONAS.map((p) => {
                      const isActive = persona === p.id;
                      return (
                        <button
                          key={p.id}
                          onClick={() => {
                            onPersonaChange(p.id === "none" ? "" : p.id);
                            if (p.id !== "none") onRecipeChange("");
                          }}
                          className={cn(
                            "w-full text-left px-2.5 sm:px-3 py-2 sm:py-2.5 rounded-lg flex items-center gap-2.5 sm:gap-3 transition-all",
                            "hover:bg-muted/50 hover:scale-[1.02] active:scale-[0.98]",
                            isActive && "bg-primary/10 text-primary ring-1 ring-primary/20",
                          )}
                        >
                          <span className={cn(
                            "p-1.5 rounded-md shrink-0",
                            isActive ? "bg-primary/20 text-primary" : "bg-muted",
                          )}>
                            {renderPersonaIcon(p.id)}
                          </span>
                          <div className="flex-1 min-w-0">
                            <div className={cn("font-medium text-xs sm:text-sm", isActive && "text-primary")}>
                              {p.name}
                            </div>
                            <p className="text-[10px] sm:text-xs text-muted-foreground line-clamp-1">
                              {p.description}
                            </p>
                          </div>
                          {isActive && <Check size={14} className="text-primary" />}
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Recipes Section */}
                <div>
                  <div className="text-[10px] sm:text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 px-2">
                    Recipe
                  </div>
                  <div className="space-y-1">
                    {RECIPES.map((r) => {
                      const isActive = recipe === r.id;
                      return (
                        <button
                          key={r.id}
                          onClick={() => {
                            onRecipeChange(r.id === "none" ? "" : r.id);
                            if (r.id !== "none") onPersonaChange("");
                          }}
                          className={cn(
                            "w-full text-left px-2.5 sm:px-3 py-2 sm:py-2.5 rounded-lg flex items-center gap-2.5 sm:gap-3 transition-all",
                            "hover:bg-muted/50 hover:scale-[1.02] active:scale-[0.98]",
                            isActive && "bg-primary/10 text-primary ring-1 ring-primary/20",
                          )}
                        >
                          <span className={cn(
                            "p-1.5 rounded-md shrink-0",
                            isActive ? "bg-primary/20 text-primary" : "bg-muted",
                          )}>
                            {renderRecipeIcon(r.id)}
                          </span>
                          <div className="flex-1 min-w-0">
                            <div className={cn("font-medium text-xs sm:text-sm", isActive && "text-primary")}>
                              {r.name}
                            </div>
                            <p className="text-[10px] sm:text-xs text-muted-foreground line-clamp-1">
                              {r.description}
                            </p>
                          </div>
                          {isActive && <Check size={14} className="text-primary" />}
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* Tools Tab */}
            {activeTab === "tools" && showToolsTab && (
              <div className="p-3 sm:p-4">
                {/* Search */}
                {availableTools.length > 8 && (
                  <div className="relative mb-3">
                    <Search
                      size={14}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                    />
                    <input
                      type="text"
                      placeholder="Search tools..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-9 pr-3 py-2 text-xs sm:text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20"
                    />
                  </div>
                )}

                {!modelSupportsTools ? (
                  <div className="text-center py-8 text-sm text-muted-foreground">
                    {modelDisplayName} doesn't support tools
                  </div>
                ) : (
                  <div className="space-y-3">
                    {/* Regular Tools */}
                    {getFilteredTools(categorizedTools.regular).length > 0 && (
                      <div>
                        <div className="text-[10px] sm:text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 px-2">
                          General Tools
                        </div>
                        <div className="space-y-1">
                          {getFilteredTools(categorizedTools.regular).map((tool) => {
                            const isEnabled = enabledTools.includes(tool.id);
                            const IconComponent = tool.icon || (() => renderToolIcon(tool.id));
                            return (
                              <button
                                key={tool.id}
                                onClick={() => onToggleTool(tool.id)}
                                className={cn(
                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
                                  "hover:bg-muted/50",
                                  isEnabled && "bg-primary/10 text-primary",
                                )}
                              >
                                <span className={cn(
                                  "p-1.5 rounded-md shrink-0",
                                  isEnabled ? "bg-primary/20 text-primary" : "bg-muted",
                                )}>
                                  <IconComponent size={14} />
                                </span>
                                <div className="flex-1 min-w-0">
                                  <div className={cn("font-medium text-xs sm:text-sm", isEnabled && "text-primary")}>
                                    {tool.name}
                                  </div>
                                </div>
                                <Switch
                                  checked={isEnabled}
                                  className="h-4 w-7"
                                />
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {/* MCP Tools */}
                    {getFilteredTools(categorizedTools.mcp).length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2 px-2 flex items-center gap-1">
                          <Bot size={12} />
                          MCP Tools
                        </div>
                        <div className="space-y-1">
                          {getFilteredTools(categorizedTools.mcp).map((tool) => {
                            const isEnabled = enabledTools.includes(tool.id);
                            const IconComponent = tool.icon || Bot;
                            return (
                              <button
                                key={tool.id}
                                onClick={() => onToggleTool(tool.id)}
                                className={cn(
                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
                                  "hover:bg-muted/50",
                                  isEnabled && "bg-primary/10 text-primary",
                                )}
                              >
                                <span className={cn(
                                  "p-1.5 rounded-md shrink-0",
                                  isEnabled ? "bg-primary/20 text-primary" : "bg-muted",
                                )}>
                                  <IconComponent size={14} />
                                </span>
                                <div className="flex-1 min-w-0">
                                  <div className={cn("font-medium text-sm", isEnabled && "text-primary")}>
                                    {tool.name.startsWith("MCP: ") ? tool.name.replace(/^MCP: /, "") : tool.name}
                                  </div>
                                </div>
                                <Switch
                                  checked={isEnabled}
                                  className="h-4 w-7"
                                />
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {/* n8n Tools */}
                    {getFilteredTools(categorizedTools.n8n).length > 0 && (
                      <div>
                        <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2 px-2 flex items-center gap-1">
                          <Zap size={12} />
                          n8n Workflows
                        </div>
                        <div className="space-y-1">
                          {getFilteredTools(categorizedTools.n8n).map((tool) => {
                            const isEnabled = enabledTools.includes(tool.id);
                            const IconComponent = tool.icon || Zap;
                            return (
                              <button
                                key={tool.id}
                                onClick={() => onToggleTool(tool.id)}
                                className={cn(
                                  "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
                                  "hover:bg-muted/50",
                                  isEnabled && "bg-primary/10 text-primary",
                                )}
                              >
                                <span className={cn(
                                  "p-1.5 rounded-md shrink-0",
                                  isEnabled ? "bg-primary/20 text-primary" : "bg-muted",
                                )}>
                                  <IconComponent size={14} />
                                </span>
                                <div className="flex-1 min-w-0">
                                  <div className={cn("font-medium text-xs sm:text-sm", isEnabled && "text-primary")}>
                                    {tool.name}
                                  </div>
                                </div>
                                <Switch
                                  checked={isEnabled}
                                  className="h-4 w-7"
                                />
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Thinking Tab */}
            {activeTab === "thinking" && showThinkingTab && (
              <div className="p-3">
                <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-3 px-2">
                  Reasoning Effort
                </div>
                <div className="space-y-1">
                  {modelThinkingBudgets.map((budget) => {
                    const isActive = thinkingBudget === budget;
                    const label = getThinkingBudgetLabel(budget);
                    
                    // Get an icon based on the budget level
                    const getIcon = () => {
                      const numBudget = typeof budget === "number" ? budget : Number(budget);
                      if (!isNaN(numBudget)) {
                        if (numBudget === 0) return <Brain size={14} className="opacity-30" />;
                        if (numBudget <= 3000) return <Brain size={14} className="opacity-50" />;
                        if (numBudget <= 8000) return <Brain size={14} className="opacity-70" />;
                        if (numBudget <= 15000) return <Brain size={14} className="opacity-90" />;
                        return <Brain size={14} />;
                      }
                      return <Brain size={14} />;
                    };

                    return (
                      <button
                        key={String(budget)}
                        onClick={() => onThinkingBudgetChange?.(budget)}
                        className={cn(
                          "w-full text-left px-3 py-2.5 rounded-lg flex items-center gap-3 transition-all",
                          "hover:bg-muted/50",
                          isActive && "bg-primary/10 text-primary",
                        )}
                      >
                        <span className={cn(
                          "p-1.5 rounded-md",
                          isActive ? "bg-primary/20 text-primary" : "bg-muted",
                        )}>
                          {getIcon()}
                        </span>
                        <div className="flex-1 min-w-0">
                          <div className={cn("font-medium text-sm", isActive && "text-primary")}>
                            {label}
                          </div>
                          {typeof budget === "number" && budget > 0 && (
                            <p className="text-xs text-muted-foreground">
                              Up to {budget.toLocaleString()} tokens
                            </p>
                          )}
                        </div>
                        {isActive && <Check size={14} className="text-primary" />}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>,
        document.body,
      )}
    </div>
  );
}
