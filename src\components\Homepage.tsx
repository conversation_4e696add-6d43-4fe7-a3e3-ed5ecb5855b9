import { <PERSON><PERSON> } from "./ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { 
  Brain, 
  Sparkles, 
  Zap, 
  Shield, 
  Users, 
  MessageSquare, 
  ArrowRight,
  Star,
  ChevronRight,
  Globe,
  Code,
  Search,
  Image,
  FastForward,
  Link2,
  Sparkles as Sparks,
  Check,
  Clock,
  Github,
  Twitter,
  CircleHelpIcon
} from "lucide-react";
import { ParticlesBackground } from "./ParticlesBackground";

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "./ui/navigation-menu";

import { cn } from "../lib/utils";

import { Menu } from "lucide-react";

import { Sheet, SheetContent, SheetTrigger } from "./ui/sheet";

import React, { useState, useEffect } from "react";
import { Switch } from "./ui/switch";
import { Accordion, AccordionContent, Accordion<PERSON><PERSON>, AccordionTrigger } from "./ui/accordion";

import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";

const features = [
  {
    icon: Brain,
    title: "Unified AI Powerhouse",
    description: "Seamlessly access the world's most advanced AI models—GPT-4o, Claude 4, Gemini 2.5, and dozens more—through a single, intuitive platform."
  },
  {
    icon: Zap,
    title: "Lightning-Fast Interactions",
    description: "Experience instant, streaming responses as you type. ErzenAI delivers answers in real time, keeping your workflow in perfect sync."
  },
  {
    icon: Shield,
    title: "Ironclad Privacy & Security",
    description: "Your data is yours. With enterprise-grade encryption and Privacy Mode, your conversations stay private and protected."
  },
  {
    icon: Code,
    title: "AI-Driven Coding Genius",
    description: "Refactor, debug, and generate code in over 50 languages. ErzenAI accelerates your development with context-aware suggestions."
  },
  {
    icon: Search,
    title: "Live Knowledge Engine",
    description: "Tap into real-time web search and documentation lookup. Every answer is fresh, cited, and actionable."
  },
  {
    icon: MessageSquare,
    title: "Persistent Smart Memory",
    description: "ErzenAI remembers your goals, preferences, and project context—so every reply is tailored to you."
  }
];

const stats = [
  { label: "AI Models", value: "75+" },
  { label: "Supported Languages", value: "50+" },
  { label: "System Uptime", value: "99.9%" },
  { label: "Avg. Response Time", value: "<2s" }
];

const pricingPlans = [
  {
    name: "Starter",
    price: "$0",
    annualPrice: "$0",
    period: "forever",
    description: "Ideal for exploring ErzenAI's capabilities and starting your AI journey.",
    credits: 100,
    searches: 10,
    maxSpending: "$1.00",
    features: [
      "100 monthly AI credits",
      "10 web lookups per month",
      "Access to all core models",
      "Real-time streaming replies",
      "Basic project memory",
      "Community support"
    ],
    popular: false,
    available: true,
    buttonText: "Start Free"
  },
  {
    name: "Creator",
    price: "$15",
    annualPrice: "$144",
    period: "month",
    description: "For makers and professionals who want more power and flexibility.",
    credits: 500,
    searches: 200,
    maxSpending: "$10.00",
    features: [
      "500 monthly AI credits",
      "200 web lookups per month",
      "Priority access to premium models",
      "Faster response speeds",
      "Advanced memory & sharing",
      "Custom AI instructions",
      "Priority support",
      "Export project history"
    ],
    popular: true,
    available: true,
    buttonText: "Upgrade Now"
  },
  {
    name: "Visionary",
    price: "$40",
    annualPrice: "$384",
    period: "month",
    description: "For teams and power users building the future with AI.",
    credits: 2500,
    searches: 1000,
    maxSpending: "$25.00",
    features: [
      "2,500 monthly AI credits",
      "1,000 web lookups per month",
      "Unlimited access to select models",
      "Team collaboration tools",
      "Custom model fine-tuning",
      "API & integrations",
      "Dedicated account manager"
    ],
    popular: false,
    available: true,
    buttonText: "Go Visionary"
  },
  {
    name: "Enterprise",
    price: "$200",
    annualPrice: "$1,920",
    period: "month",
    description: "For organizations demanding scale, security, and custom solutions.",
    credits: 20000,
    searches: 5000,
    maxSpending: "$120.00",
    features: [
      "20,000 monthly AI credits",
      "5,000 web lookups per month",
      "Unlimited premium model access",
      "Enterprise-grade infrastructure",
      "White-label & on-premise options",
      "SLA & compliance guarantees",
      "24/7 dedicated support"
    ],
    popular: false,
    available: true,
    buttonText: "Contact Sales"
  }
];

const faqs = [
  {
    q: "How does ErzenAI use credits?",
    a: "Credits are based on the actual AI model usage. Each model has a different cost per token, and your credits reset monthly according to your plan. You can always track your usage in your dashboard.",
  },
  {
    q: "Can I change my plan whenever I want?",
    a: "Absolutely! You can upgrade, downgrade, or cancel at any time. Changes are applied instantly, and billing is always fair and prorated.",
  },
  {
    q: "Is my data private and secure?",
    a: "Yes. ErzenAI is built with privacy at its core. Your conversations are encrypted, and with Privacy Mode enabled, nothing leaves your device. We are fully SOC 2 compliant.",
  },
  {
    q: "What payment methods are supported?",
    a: "We accept all major credit cards, Apple Pay, Google Pay, and PayPal via our secure Stripe integration. All transactions are encrypted and protected.",
  },
  {
    q: "What happens if I use my own API keys?",
    a: "When you add your own API keys, you unlock unlimited usage for supported models. Your keys are encrypted and only used for your requests—no credit counting applies.",
  },
  {
    q: "When do credits reset?",
    a: "Credits reset every 30 days for free users, and on your billing cycle for paid plans. Upgrading your plan also resets your usage immediately.",
  },
  {
    q: "Can I export my data?",
    a: "Yes! You can export your entire conversation and project history at any time from your settings. Sharing and backup are always in your control.",
  },
  {
    q: "Which AI models are available?",
    a: "ErzenAI gives you access to 75+ models, including GPT-4o, Claude 4, Gemini 2.5 Pro, DeepSeek R1, and more. All plans include core models, with higher tiers unlocking unlimited premium access.",
  }
];

const advancedFeatures = [
  {
    icon: Sparkles,
    title: "Memory",
    description: "ErzenAI remembers your files, rules, and workflows—so you never lose your place or momentum.",
    gradient: "from-pink-400 via-yellow-400 to-purple-400"
  },
  {
    icon: Zap,
    title: "Instant Integrations",
    description: "Connect tools like Slack, Stripe, GitHub, and more—no code required. Powered by MCP servers, ErzenAI brings all your favorite services together in seconds.",
    gradient: "from-blue-500 via-indigo-500 to-purple-600"
  },
  {
    icon: Link2,
    title: "Workflow Automation",
    description: "Trigger complex automations and orchestrate n8n workflows directly from chat. Let ErzenAI and n8n handle the busywork, so you can focus on what matters.",
    gradient: "from-teal-500 via-cyan-500 to-blue-500"
  },
  {
    icon: Image,
    title: "Visual Intelligence",
    description: "Drop in images to generate UI, analyze content, or spark new ideas—instantly and intuitively.",
    gradient: "from-blue-600 via-indigo-600 to-purple-700"
  },
  {
    icon: ArrowRight,
    title: "Seamless Continuation",
    description: "Ask ErzenAI to pick up any task where you left off. Your progress is always saved and ready.",
    gradient: "from-purple-600 via-pink-600 to-orange-500"
  },
  {
    icon: FastForward,
    title: "Autonomous Agent Mode",
    description: "Let ErzenAI plan and execute multi-step tasks for you—end-to-end, hands-free.",
    gradient: "from-yellow-400 via-orange-500 to-red-500"
  }
];

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem";

// Navigation schema for header
/**
 * Homepage Component - Dark Mode Only
 * 
 * This component is designed exclusively for dark mode and uses
 * sophisticated dark-themed styling that is not compatible with light mode.
 * The component automatically forces dark mode via the "dark" class.
 */

const navigationGroups = [
  {
    label: "Product",
    items: [
      { title: "Features", href: "#features", description: "Explore all AI-powered features." },
      { title: "Integrations", href: "#superpowers", description: "Connect with Slack, GitHub, Stripe, and more." },
      { title: "Memory", href: "#superpowers", description: "Contextual memory for your projects." },
      { title: "Automations", href: "#superpowers", description: "Automate workflows with n8n and MCP servers." },
      { title: "Visual Intelligence", href: "#superpowers", description: "Image understanding and UI generation." },
      { title: "Agent Mode", href: "#superpowers", description: "Autonomous multi-step task execution." },
    ],
  },
  {
    label: "Solutions",
    items: [
      { title: "For Teams", href: "#pricing", description: "Collaboration and productivity for teams." },
      { title: "For Developers", href: "#features", description: "Powerful tools for coding and automation." },
      { title: "For Enterprises", href: "#pricing", description: "Scale, security, and compliance." },
      { title: "Education", href: "#about", description: "AI for learning and research." },
    ],
  },
  {
    label: "Resources",
    items: [
      { title: "Docs", href: "/docs", description: "Developer and user documentation." },
      { title: "Blog", href: "/blog", description: "Insights, guides, and news." },
      { title: "Changelog", href: "/changelog", description: "Latest updates and releases." },
      { title: "Community", href: "/community", description: "Join our user community." },
      { title: "Forum", href: "/forum", description: "Ask questions and get help." },
      { title: "Support", href: "/support", description: "Contact our support team." },
    ],
  },
  {
    label: "Company",
    items: [
      { title: "About", href: "#about", description: "Learn about ErzenAI." },
      { title: "Careers", href: "/careers", description: "Work with us." },
      { title: "Customers", href: "/customers", description: "See who uses ErzenAI." },
      { title: "Security", href: "/security", description: "Our security practices." },
      { title: "Privacy", href: "/privacy", description: "Privacy policy." },
      { title: "Terms", href: "/terms", description: "Terms of service." },
    ],
  },
];

export function Homepage() {
  const handleGetStarted = () => {
    window.history.pushState({}, '', '/login');
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  const handleLearnMore = () => {
    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
  };

  const [isAnnual, setIsAnnual] = useState(false);

  const placeholders = [
    "Plan a marketing strategy for a new SaaS product...",
    "Write a python script to scrape website data...",
    "Generate a photorealistic image of a futuristic city...",
    "Summarize the attached research paper on quantum computing...",
    "Debug this React component and explain the issue...",
  ];
  const [placeholder, setPlaceholder] = useState(placeholders[0]);
  const [inputValue, setInputValue] = useState("");

  useEffect(() => {
    let index = 0;
    const interval = setInterval(() => {
      index = (index + 1) % placeholders.length;
      setPlaceholder(placeholders[index]);
    }, 40);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="dark bg-black text-gray-300 antialiased">
        {/* Header */}
      <header className="sticky top-0 z-50 bg-black/50 backdrop-blur-lg border-b border-white/10">
          <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img src="/icon0.svg" alt="ErzenAI logo" className="h-8 w-8 rounded-lg" />
            <span className="text-2xl font-bold text-white">ErzenAI</span>
            </div>

          <div className="hidden md:flex items-center space-x-6">
            <NavigationMenu>
                <NavigationMenuList>
                  {navigationGroups.map((group, idx) => (
                    <NavigationMenuItem key={group.label}>
                  <NavigationMenuTrigger className="text-sm font-medium text-gray-300 hover:text-white transition-colors bg-transparent border-none">
                        {group.label}
                  </NavigationMenuTrigger>
                    <NavigationMenuContent>
                        <div className="grid gap-3 p-6 md:w-[400px] lg:w-[500px]">
                          {group.items.map((item) => (
                            <NavigationMenuLink asChild key={item.title}>
                          <a
                                href={item.href}
                                className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-white/5 hover:text-white"
                          >
                                <div className="text-sm font-medium leading-none text-white">{item.title}</div>
                                <p className="line-clamp-2 text-sm leading-snug text-gray-400">{item.description}</p>
                          </a>
                        </NavigationMenuLink>
                          ))}
                    </div>
                    </NavigationMenuContent>
                  </NavigationMenuItem>
                  ))}
                </NavigationMenuList>
              </NavigationMenu>
          </div>

          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2">
              <Button variant="ghost" onClick={handleGetStarted} className="text-sm font-medium hover:text-white transition-colors">Log in</Button>
              <Button onClick={handleGetStarted} size="sm" className="bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium px-4">Sign Up</Button>
            </div>
            
            {/* Mobile Menu */}
              <div className="md:hidden">
                <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon"><Menu className="h-6 w-6" /></Button>
                  </SheetTrigger>
                <SheetContent side="right" className="bg-black border-l border-white/10 text-white w-[250px]">
                  <div className="flex flex-col h-full">
                    <nav className="flex flex-col space-y-5 mt-8 text-base">
                      {navigationGroups.map((group) => (
                        <div key={group.label} className="mb-4">
                          <div className="font-semibold text-gray-400 uppercase text-xs mb-2">{group.label}</div>
                          {group.items.map((item) => (
                            <a
                              key={item.title}
                              href={item.href}
                              className="block py-1.5 px-2 rounded hover:bg-white/10 hover:text-white transition-colors text-gray-300"
                            >
                              {item.title}
                            </a>
                          ))}
                        </div>
                      ))}
                    </nav>
                    <div className="mt-auto border-t border-white/10 pt-6 space-y-4">
                      <Button variant="outline" onClick={handleGetStarted} className="w-full border-white/20 hover:bg-white/10 hover:text-white">Log in</Button>
                      <Button onClick={handleGetStarted} className="w-full bg-blue-600 hover:bg-blue-700">Sign Up</Button>
                          </div>
                          </div>
                  </SheetContent>
                </Sheet>
              </div>
            </div>
          </div>
        </header>

      <main>
        {/* Hero Section */}
        <section className="relative min-h-screen flex flex-col items-center justify-center px-4 text-center overflow-hidden">
          {/* Sophisticated Hero Background Effects */}
          <div className="absolute inset-0 bg-black">
            {/* Premium Central Gradient */}
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[1000px] bg-gradient-to-br from-zinc-800/20 via-slate-800/15 to-gray-800/20 rounded-full blur-[400px] animate-pulse-slow opacity-80" />
            
            {/* Elegant Floating Orbs */}
            <div className="absolute top-16 left-16 w-80 h-80 bg-gradient-to-br from-zinc-700/25 to-transparent rounded-full blur-3xl animate-float opacity-60" />
            <div className="absolute bottom-16 right-16 w-96 h-96 bg-gradient-to-tl from-slate-700/30 to-transparent rounded-full blur-3xl animate-float animation-delay-3000 opacity-70" />
            <div className="absolute top-32 right-1/3 w-64 h-64 bg-gradient-to-bl from-gray-700/20 to-transparent rounded-full blur-2xl animate-float animation-delay-1500 opacity-50" />
            <div className="absolute bottom-32 left-1/3 w-72 h-72 bg-gradient-to-tr from-zinc-600/25 to-transparent rounded-full blur-3xl animate-float animation-delay-4000 opacity-65" />
            
            {/* Subtle Geometric Elements */}
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-zinc-400/60 rounded-full animate-pulse opacity-80" />
            <div className="absolute top-2/3 right-1/4 w-1.5 h-1.5 bg-slate-400/50 rounded-full animate-pulse animation-delay-2000 opacity-70" />
            <div className="absolute bottom-1/4 left-2/3 w-2.5 h-2.5 bg-gray-400/40 rounded-full animate-pulse animation-delay-1000 opacity-60" />
            <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-zinc-300/70 rounded-full animate-pulse animation-delay-3500 opacity-50" />
            
            {/* Refined Ring Effects */}
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] border border-zinc-700/10 rounded-full animate-spin-slow" />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] border border-slate-700/8 rounded-full animate-spin-slow animation-delay-2000" style={{animationDirection: 'reverse'}} />
            
            {/* Premium Grid Pattern */}
            <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.008)_1px,transparent_1px)] bg-[size:100px_100px] opacity-40" />
            
            {/* Sophisticated Gradient Overlays */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
            <div className="absolute inset-0 bg-gradient-to-b from-zinc-900/5 via-transparent to-slate-900/5" />
          </div>

          {/* Premium Border Effects */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-zinc-500/30 to-transparent" />
          <div className="absolute top-0 left-1/2 w-[150%] h-px -translate-x-1/2 bg-gradient-to-r from-transparent via-zinc-300/20 to-transparent animate-aurora-border" />
          
          <div className="max-w-5xl relative z-10">
            {/* Enhanced Typography */}
            <div className="mb-8">
              <div className="flex justify-center mb-6">
                <div className="flex items-center space-x-2 bg-zinc-900/50 backdrop-blur-sm border border-zinc-800/50 rounded-full px-4 py-2">
                  <div className="w-2 h-2 bg-zinc-400/60 rounded-full animate-pulse" />
                  <span className="text-xs text-zinc-400 font-medium tracking-wide uppercase">AI-Powered Workspace</span>
                </div>
              </div>
              
              <h1 className="text-display-xl text-white mb-8 animate-fade-in">
                <span className="bg-gradient-to-br from-white via-zinc-100 to-zinc-300 bg-clip-text text-transparent">
                  Conversations at the
                </span>
                <br />
                <span className="bg-gradient-to-br from-zinc-200 via-zinc-300 to-zinc-400 bg-clip-text text-transparent">
                  Speed of Thought
                </span>
              </h1>
            </div>
            
            <p className="text-body-xl text-zinc-300 max-w-4xl mx-auto mb-12 animate-fade-in animation-delay-300">
              ErzenAI transforms ideas into reality with frontier models, instant streaming responses, and autonomous execution—all in one intelligent workspace.
            </p>

            {/* Premium Input Design */}
            <div className="relative max-w-2xl mx-auto animate-fade-in animation-delay-600">
              <form onSubmit={(e) => { e.preventDefault(); handleGetStarted(); }} className="relative">
                {/* Input Container with Enhanced Styling */}
                <div className="relative group">
                  {/* Glow Effect */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/20 to-zinc-600/0 rounded-full opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-500 blur-sm" />
                  
                  {/* Main Input */}
                  <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/40 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-full p-1 transition-all duration-300 group-hover:border-zinc-700/80 group-focus-within:border-zinc-600/80">
                    <input 
                      type="text"
                      placeholder={placeholder}
                      value={inputValue}
                      onChange={e => setInputValue(e.target.value)}
                      className="w-full h-16 pl-8 pr-48 bg-transparent text-white placeholder-zinc-500 focus:outline-none text-lg font-medium tracking-wide"
                    />
                    
                    {/* Enhanced Submit Button */}
                    <Button 
                      type="submit"
                      className="absolute top-1/2 right-1 -translate-y-1/2 h-14 px-8 rounded-full bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white font-semibold flex items-center gap-3 transition-all duration-300 hover:scale-105 border border-zinc-700/50 hover:border-zinc-600/70 shadow-xl"
                    >
                      <span>Start Building</span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {/* Input Enhancement Details */}
                <div className="flex justify-center items-center mt-6 space-x-6 text-sm text-zinc-500">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-zinc-500/60 rounded-full" />
                    <span>No credit card required</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-zinc-500/60 rounded-full" />
                    <span>Start in seconds</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-zinc-500/60 rounded-full" />
                    <span>75+ AI models</span>
                  </div>
                </div>
              </form>
            </div>

            {/* Enhanced Stats Section */}
            <div className="mt-16 animate-fade-in animation-delay-900">
              <div className="flex flex-wrap justify-center gap-8 md:gap-12">
                {[
                  { value: "75+", label: "AI Models" },
                  { value: "99.9%", label: "Uptime" },
                  { value: "<2s", label: "Response Time" },
                  { value: "50+", label: "Languages" }
                ].map((stat, idx) => (
                  <div key={idx} className="text-center group">
                    <div className="text-2xl md:text-3xl font-bold text-white mb-1 group-hover:text-zinc-200 transition-colors duration-300">
                      {stat.value}
                    </div>
                    <div className="text-sm text-zinc-500 uppercase tracking-wider font-medium">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-32 px-4 bg-black relative overflow-hidden">
          {/* Sophisticated Background Effects */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-slate-800/20 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-tl from-zinc-800/30 to-transparent rounded-full blur-3xl" />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-gradient-to-br from-gray-900/15 to-transparent rounded-full blur-[200px]" />
          </div>
          
          <div className="container mx-auto relative z-10">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-extrabold text-white mb-6 tracking-tight">
                Built to Accelerate Your Workflow
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                All the AI power you need—streaming, coding, search, memory—perfectly packaged for makers and teams.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {[
                {
                  title: "Unified AI Powerhouse",
                  desc: "Seamlessly access the world's most advanced AI models—GPT-4o, Claude 4, Gemini 2.5, and dozens more—through a single, intuitive platform.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-12 h-12 border border-white/20 rounded-full" />
                      <div className="absolute bottom-6 left-6 w-8 h-8 border border-white/10 rounded-full" />
                    </div>
                  )
                },
                {
                  title: "Lightning-Fast Interactions",
                  desc: "Experience instant, streaming responses as you type. ErzenAI delivers answers in real time, keeping your workflow in perfect sync.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-6 left-4 w-6 h-6 bg-white/10 rounded" />
                      <div className="absolute bottom-4 right-6 w-4 h-4 bg-white/8 rounded" />
                      <div className="absolute top-1/2 right-4 w-3 h-3 bg-white/12 rounded" />
                    </div>
                  )
                },
                {
                  title: "Ironclad Privacy & Security",
                  desc: "Your data is yours. With enterprise-grade encryption and Privacy Mode, your conversations stay private and protected.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 left-4 w-16 h-px bg-white/20" />
                      <div className="absolute top-8 left-4 w-12 h-px bg-white/15" />
                      <div className="absolute top-12 left-4 w-8 h-px bg-white/10" />
                    </div>
                  )
                },
                {
                  title: "AI-Driven Coding Genius",
                  desc: "Refactor, debug, and generate code in over 50 languages. ErzenAI accelerates your development with context-aware suggestions.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-10 h-10 border border-white/20 rotate-12" />
                      <div className="absolute bottom-6 left-6 w-6 h-6 border border-white/15 rotate-45" />
                    </div>
                  )
                },
                {
                  title: "Live Knowledge Engine",
                  desc: "Tap into real-time web search and documentation lookup. Every answer is fresh, cited, and actionable.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-2 h-2 bg-white/20 rounded-full" />
                      <div className="absolute top-8 right-8 w-2 h-2 bg-white/15 rounded-full" />
                      <div className="absolute bottom-6 left-4 w-2 h-2 bg-white/10 rounded-full" />
                    </div>
                  )
                },
                {
                  title: "Persistent Smart Memory",
                  desc: "ErzenAI remembers your goals, preferences, and project context—so every reply is tailored to you.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-6 left-6 flex space-x-1">
                        <div className="w-1 h-8 bg-white/20" />
                        <div className="w-1 h-6 bg-white/15" />
                        <div className="w-1 h-4 bg-white/10" />
                      </div>
                    </div>
                  )
                }
              ].map((feature, index) => (
                <div key={index} className="relative group cursor-pointer">
                  {/* Hover glow effect */}
                  <div className={`absolute -inset-1 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm ${
                    index === 0 ? 'bg-gradient-to-r from-zinc-600/0 via-zinc-500/8 to-zinc-600/0' :
                    index === 1 ? 'bg-gradient-to-r from-slate-600/0 via-slate-500/8 to-slate-600/0' :
                    index === 2 ? 'bg-gradient-to-r from-gray-600/0 via-gray-500/8 to-gray-600/0' :
                    index === 3 ? 'bg-gradient-to-r from-stone-600/0 via-stone-500/8 to-stone-600/0' :
                    index === 4 ? 'bg-gradient-to-r from-neutral-600/0 via-neutral-500/8 to-neutral-600/0' :
                    'bg-gradient-to-r from-zinc-600/0 via-zinc-500/8 to-zinc-600/0'
                  }`} />
                  {/* Main card */}
                  <div className={`relative rounded-2xl p-8 transition-all duration-300 backdrop-blur-sm group-hover:transform group-hover:scale-[1.02] group-hover:shadow-2xl min-h-[260px] flex flex-col ${
                    index === 0 ? 'bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 border border-zinc-800/40 hover:border-zinc-700/60 hover:from-zinc-900/80 hover:via-slate-900/50 hover:to-zinc-900/80' :
                    index === 1 ? 'bg-gradient-to-br from-zinc-900/60 via-gray-900/30 to-zinc-900/60 border border-zinc-800/40 hover:border-zinc-700/60 hover:from-zinc-900/80 hover:via-gray-900/50 hover:to-zinc-900/80' :
                    index === 2 ? 'bg-gradient-to-br from-zinc-900/60 via-stone-900/30 to-zinc-900/60 border border-zinc-800/40 hover:border-zinc-700/60 hover:from-zinc-900/80 hover:via-stone-900/50 hover:to-zinc-900/80' :
                    index === 3 ? 'bg-gradient-to-br from-zinc-900/60 via-neutral-900/30 to-zinc-900/60 border border-zinc-800/40 hover:border-zinc-700/60 hover:from-zinc-900/80 hover:via-neutral-900/50 hover:to-zinc-900/80' :
                    index === 4 ? 'bg-gradient-to-br from-zinc-900/60 via-slate-800/30 to-zinc-900/60 border border-zinc-800/40 hover:border-zinc-700/60 hover:from-zinc-900/80 hover:via-slate-800/50 hover:to-zinc-900/80' :
                    'bg-gradient-to-br from-zinc-900/60 via-gray-800/30 to-zinc-900/60 border border-zinc-800/40 hover:border-zinc-700/60 hover:from-zinc-900/80 hover:via-gray-800/50 hover:to-zinc-900/80'
                  }`}>
                    {feature.bgPattern}
                    <div className="relative z-10 space-y-4 flex-grow flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                          index === 0 ? 'bg-zinc-500/60 group-hover:bg-zinc-400/80' :
                          index === 1 ? 'bg-slate-500/60 group-hover:bg-slate-400/80' :
                          index === 2 ? 'bg-gray-500/60 group-hover:bg-gray-400/80' :
                          index === 3 ? 'bg-stone-500/60 group-hover:bg-stone-400/80' :
                          index === 4 ? 'bg-neutral-500/60 group-hover:bg-neutral-400/80' :
                          'bg-zinc-500/60 group-hover:bg-zinc-400/80'
                        }`} />
                        <div className="text-xs text-zinc-600 font-mono group-hover:text-zinc-500 transition-colors duration-300">
                          {String(index + 1).padStart(2, '0')}
                        </div>
                      </div>
                      <h3 className="text-xl font-semibold text-white mb-3 tracking-tight group-hover:text-white transition-colors duration-300">
                        {feature.title}
                      </h3>
                      <p className="text-gray-400 leading-relaxed text-sm group-hover:text-gray-300 transition-colors duration-300 flex-grow">
                        {feature.desc}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Superpowers Section */}
        <section id="superpowers" className="py-32 px-4 bg-black relative overflow-hidden">
          {/* Sophisticated Background Effects */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-slate-800/20 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-tl from-zinc-800/30 to-transparent rounded-full blur-3xl" />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-gradient-to-br from-gray-900/15 to-transparent rounded-full blur-[200px]" />
          </div>
          <div className="container mx-auto relative z-10">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-extrabold text-white mb-6 tracking-tight">
                One Editor, Unlimited Superpowers
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                Advanced capabilities designed to accelerate your workflow and enhance productivity at every step.
              </p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {[
                {
                  title: "Memory",
                  desc: "ErzenAI remembers your files, rules, and workflows—so you never lose your place or momentum.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-12 h-12 border border-white/20 rounded-full" />
                      <div className="absolute bottom-6 left-6 w-8 h-8 border border-white/10 rounded-full" />
                    </div>
                  )
                },
                {
                  title: "Instant Integrations",
                  desc: "Connect tools like Slack, Stripe, GitHub, and more—no code required. Powered by MCP servers, ErzenAI brings all your favorite services together in seconds.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-6 left-4 w-6 h-6 bg-white/10 rounded" />
                      <div className="absolute bottom-4 right-6 w-4 h-4 bg-white/8 rounded" />
                      <div className="absolute top-1/2 right-4 w-3 h-3 bg-white/12 rounded" />
                    </div>
                  )
                },
                {
                  title: "Workflow Automation",
                  desc: "Trigger complex automations and orchestrate n8n workflows directly from chat. Let ErzenAI and n8n handle the busywork, so you can focus on what matters.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 left-4 w-16 h-px bg-white/20" />
                      <div className="absolute top-8 left-4 w-12 h-px bg-white/15" />
                      <div className="absolute top-12 left-4 w-8 h-px bg-white/10" />
                    </div>
                  )
                },
                {
                  title: "Visual Intelligence",
                  desc: "Drop in images to generate UI, analyze content, or spark new ideas—instantly and intuitively.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-10 h-10 border border-white/20 rotate-12" />
                      <div className="absolute bottom-6 left-6 w-6 h-6 border border-white/15 rotate-45" />
                    </div>
                  )
                },
                {
                  title: "Seamless Continuation",
                  desc: "Ask ErzenAI to pick up any task where you left off. Your progress is always saved and ready.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-2 h-2 bg-white/20 rounded-full" />
                      <div className="absolute top-8 right-8 w-2 h-2 bg-white/15 rounded-full" />
                      <div className="absolute bottom-6 left-4 w-2 h-2 bg-white/10 rounded-full" />
                    </div>
                  )
                },
                {
                  title: "Autonomous Agent Mode",
                  desc: "Let ErzenAI plan and execute multi-step tasks for you—end-to-end, hands-free.",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-6 left-6 flex space-x-1">
                        <div className="w-1 h-8 bg-white/20" />
                        <div className="w-1 h-6 bg-white/15" />
                        <div className="w-1 h-4 bg-white/10" />
                      </div>
                    </div>
                  )
                }
              ].map((card, idx) => (
                <div key={idx} className="relative group cursor-pointer">
                  {/* Hover glow effect */}
                  <div className={`absolute -inset-1 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm ${
                    idx === 0 ? 'bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0' :
                    idx === 1 ? 'bg-gradient-to-r from-slate-600/0 via-slate-500/10 to-slate-600/0' :
                    idx === 2 ? 'bg-gradient-to-r from-gray-600/0 via-gray-500/10 to-gray-600/0' :
                    idx === 3 ? 'bg-gradient-to-r from-stone-600/0 via-stone-500/10 to-stone-600/0' :
                    idx === 4 ? 'bg-gradient-to-r from-neutral-600/0 via-neutral-500/10 to-neutral-600/0' :
                    'bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0'
                  }`} />
                  {/* Main card */}
                  <div className={`relative rounded-2xl p-8 transition-all duration-300 backdrop-blur-sm group-hover:transform group-hover:scale-[1.02] group-hover:shadow-2xl min-h-[280px] flex flex-col ${
                    idx === 0 ? 'bg-gradient-to-br from-zinc-900/70 via-slate-900/40 to-zinc-900/70 border border-zinc-800/50 hover:border-zinc-700/70 hover:from-zinc-900/90 hover:via-slate-900/60 hover:to-zinc-900/90' :
                    idx === 1 ? 'bg-gradient-to-br from-zinc-900/70 via-gray-900/40 to-zinc-900/70 border border-zinc-800/50 hover:border-zinc-700/70 hover:from-zinc-900/90 hover:via-gray-900/60 hover:to-zinc-900/90' :
                    idx === 2 ? 'bg-gradient-to-br from-zinc-900/70 via-stone-900/40 to-zinc-900/70 border border-zinc-800/50 hover:border-zinc-700/70 hover:from-zinc-900/90 hover:via-stone-900/60 hover:to-zinc-900/90' :
                    idx === 3 ? 'bg-gradient-to-br from-zinc-900/70 via-neutral-900/40 to-zinc-900/70 border border-zinc-800/50 hover:border-zinc-700/70 hover:from-zinc-900/90 hover:via-neutral-900/60 hover:to-zinc-900/90' :
                    idx === 4 ? 'bg-gradient-to-br from-zinc-900/70 via-slate-800/40 to-zinc-900/70 border border-zinc-800/50 hover:border-zinc-700/70 hover:from-zinc-900/90 hover:via-slate-800/60 hover:to-zinc-900/90' :
                    'bg-gradient-to-br from-zinc-900/70 via-gray-800/40 to-zinc-900/70 border border-zinc-800/50 hover:border-zinc-700/70 hover:from-zinc-900/90 hover:via-gray-800/60 hover:to-zinc-900/90'
                  }`}>
                    {card.bgPattern}
                    <div className="relative z-10 space-y-4 flex-grow flex flex-col">
                      <div className="flex items-center justify-between mb-4">
                        <div className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                          idx === 0 ? 'bg-zinc-500/60 group-hover:bg-zinc-400/80' :
                          idx === 1 ? 'bg-slate-500/60 group-hover:bg-slate-400/80' :
                          idx === 2 ? 'bg-gray-500/60 group-hover:bg-gray-400/80' :
                          idx === 3 ? 'bg-stone-500/60 group-hover:bg-stone-400/80' :
                          idx === 4 ? 'bg-neutral-500/60 group-hover:bg-neutral-400/80' :
                          'bg-zinc-500/60 group-hover:bg-zinc-400/80'
                        }`} />
                        <div className="text-xs text-zinc-600 font-mono group-hover:text-zinc-500 transition-colors duration-300">
                          {String(idx + 1).padStart(2, '0')}
                        </div>
                      </div>
                      <h3 className="text-xl font-semibold text-white mb-3 tracking-tight group-hover:text-white transition-colors duration-300">
                        {card.title}
                      </h3>
                      <p className="text-gray-400 leading-relaxed text-sm group-hover:text-gray-300 transition-colors duration-300 flex-grow">
                        {card.desc}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-32 px-4 bg-black relative overflow-hidden">
          {/* Section Background Effects */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-10 left-10 w-96 h-96 bg-gradient-to-br from-slate-800/30 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-tl from-zinc-800/40 to-transparent rounded-full blur-3xl" />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-br from-gray-900/20 to-transparent rounded-full blur-[200px]" />
          </div>
          <div className="container mx-auto relative z-10">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-6xl font-extrabold text-white mb-6 tracking-tight">
                Meet Your Autonomous Agent
              </h2>
              <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
                ErzenAI operates as your intelligent partner—understanding context, planning execution, and delivering results autonomously.
              </p>
            </div>
            {/* Professional Stepper UI */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-6 max-w-7xl mx-auto">
              {/* Connecting line for desktop */}
              <div className="hidden md:block absolute top-1/2 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-600/40 via-gray-600/40 to-transparent z-0" style={{transform: 'translateY(-2rem)', left: '12.5%', right: '12.5%'}} />
              {[
                {
                  title: "Understand",
                  desc: "Analyzes your request, available context, and relevant data sources with precision.",
                  number: "01",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-16 h-16 border border-white/20 rotate-45" />
                      <div className="absolute bottom-6 left-6 w-12 h-12 border border-white/10 rotate-12" />
                    </div>
                  )
                },
                {
                  title: "Strategize",
                  desc: "Develops optimal execution paths using available tools and established workflows.",
                  number: "02",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-6 left-4 w-8 h-8 bg-white/10 rounded-full" />
                      <div className="absolute bottom-4 right-6 w-6 h-6 bg-white/5 rounded-full" />
                      <div className="absolute top-1/2 right-4 w-4 h-4 bg-white/10 rounded-full" />
                    </div>
                  )
                },
                {
                  title: "Execute",
                  desc: "Implements solutions through integrated tools, APIs, and automated processes.",
                  number: "03",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 left-4 w-20 h-px bg-white/20" />
                      <div className="absolute top-8 left-4 w-16 h-px bg-white/15" />
                      <div className="absolute top-12 left-4 w-12 h-px bg-white/10" />
                    </div>
                  )
                },
                {
                  title: "Deliver",
                  desc: "Presents structured results with actionable insights and clear next steps.",
                  number: "04",
                  bgPattern: (
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-4 right-4 w-3 h-3 bg-white/20" />
                      <div className="absolute top-8 right-8 w-3 h-3 bg-white/15" />
                      <div className="absolute bottom-6 left-4 w-3 h-3 bg-white/10" />
                      <div className="absolute bottom-10 left-8 w-3 h-3 bg-white/5" />
                    </div>
                  )
                }
              ].map((step, idx) => (
                <div key={step.title} className="relative group">
                  {/* Step Card */}
                  <div className="relative bg-gradient-to-br from-zinc-900/50 to-gray-900/30 border border-zinc-800/50 rounded-2xl p-8 transition-all duration-300 hover:border-zinc-700/70 hover:bg-gradient-to-br hover:from-zinc-900/70 hover:to-gray-900/50 backdrop-blur-sm">
                    {step.bgPattern}
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-6">
                        <span className="text-6xl font-light text-zinc-600 tracking-wider">
                          {step.number}
                        </span>
                        <div className="w-2 h-2 bg-zinc-500 rounded-full opacity-60" />
                      </div>
                      <h3 className="text-2xl font-semibold text-white mb-4 tracking-tight">
                        {step.title}
                      </h3>
                      <p className="text-gray-400 leading-relaxed text-base">
                        {step.desc}
                      </p>
                    </div>
                  </div>
                  {/* Progress indicator */}
                  <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-1 h-8 bg-gradient-to-b from-zinc-700 to-transparent opacity-60" />
                </div>
              ))}
            </div>
            {/* Bottom accent line */}
            <div className="mt-16 flex justify-center">
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-zinc-600 to-transparent" />
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="py-32 px-4 bg-black relative overflow-hidden">
          {/* Sophisticated Background Effects */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-slate-800/20 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-tl from-zinc-800/30 to-transparent rounded-full blur-3xl" />
          </div>
          
          <div className="container mx-auto relative z-10">
            <div className="grid lg:grid-cols-2 gap-20 items-center">
              <div className="space-y-8">
                <div>
                  <h2 className="text-4xl md:text-6xl font-extrabold text-white mb-6 tracking-tight">
                    Engineered for Builders
                  </h2>
                  <p className="text-xl text-gray-400 mb-12 leading-relaxed max-w-2xl">
                    From cutting-edge research labs to global enterprise deployments, ErzenAI powers the next generation of ideas. Every interaction engineered for innovators shaping tomorrow.
                  </p>
                </div>
                <div className="grid gap-6">
                  {[
                    {
                      title: "Global accessibility with multi-language fluency",
                      pattern: <div className="absolute top-3 right-3 w-8 h-8 border border-zinc-600/30 rounded rotate-45" />
                    },
                    {
                      title: "Enterprise-grade security & privacy",
                      pattern: <div className="absolute top-3 right-3 w-2 h-2 bg-zinc-500/40 rounded-full" />
                    },
                    {
                      title: "Real-time responses under 2 seconds",
                      pattern: <div className="absolute top-3 right-3 w-6 h-px bg-zinc-500/40" />
                    }
                  ].map((item, idx) => (
                    <div key={idx} className="relative bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 rounded-xl p-6 transition-all duration-300 hover:border-zinc-700/60 hover:bg-gradient-to-r hover:from-zinc-900/60 hover:to-zinc-800/40">
                      {item.pattern}
                      <div className="flex items-center space-x-4">
                        <div className="w-1.5 h-1.5 bg-zinc-500 rounded-full" />
                        <span className="text-gray-300 text-lg font-medium">{item.title}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="relative">
                <div className="absolute -top-4 -left-4 w-32 h-32 border border-zinc-800/40 rounded-2xl opacity-30" />
                <div className="relative bg-gradient-to-br from-zinc-900/60 to-gray-900/40 border border-zinc-800/50 rounded-3xl p-12 backdrop-blur-sm">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-3 h-3 bg-emerald-500/80 rounded-full animate-pulse shadow-lg shadow-emerald-500/30"></div>
                        <span className="text-sm text-zinc-400 font-medium">System Active</span>
                      </div>
                      <div className="px-3 py-1 bg-zinc-800/50 text-zinc-300 text-xs font-medium rounded-full border border-zinc-700/50">AI Agent</div>
                    </div>
                    <div className="bg-zinc-800/30 rounded-xl p-5 border border-zinc-700/30">
                      <p className="text-zinc-200 text-sm leading-relaxed">Ready to assist with your development workflow</p>
                    </div>
                    <div className="bg-zinc-700/20 rounded-xl p-5 ml-8 border border-zinc-600/20">
                      <p className="text-white text-sm leading-relaxed">Generate a React component for user authentication</p>
                    </div>
                    <div className="bg-zinc-800/30 rounded-xl p-5 border border-zinc-700/30">
                      <p className="text-zinc-200 text-sm leading-relaxed">
                        I'll create a comprehensive authentication component with TypeScript support, form validation, and modern design patterns.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Platform Showcase Section */}
        <section className="py-32 px-4 bg-black relative overflow-hidden">
          {/* Sophisticated Background Effects */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-1/4 left-10 w-80 h-80 bg-gradient-to-br from-zinc-800/20 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-1/4 right-10 w-96 h-96 bg-gradient-to-tl from-slate-800/25 to-transparent rounded-full blur-3xl" />
          </div>
          
          <div className="container mx-auto relative z-10">
            <div className="grid lg:grid-cols-2 gap-20 items-center">
              <div className="relative">
                <div className="absolute -top-6 -right-6 w-40 h-40 border border-zinc-800/30 rounded-3xl opacity-20" />
                <div className="relative bg-gradient-to-br from-zinc-900/50 to-gray-900/30 border border-zinc-800/50 rounded-3xl p-2 backdrop-blur-sm shadow-2xl">
                  <img 
                    src="https://id8itbkgdp.ufs.sh/f/91vujkwlTBUVXrCTSCN71jNxsaFUAoM8wYT6PD9cltiZp5fr" 
                    alt="ErzenAI Platform Interface"
                    className="w-full rounded-2xl shadow-xl"
                  />
                </div>
              </div>
              <div className="space-y-8">
                <div>
                  <h2 className="text-4xl md:text-6xl font-extrabold text-white mb-6 tracking-tight">
                    Meet Your AI Companion
                  </h2>
                  <p className="text-xl text-gray-400 mb-12 leading-relaxed max-w-2xl">
                    A distraction-free workspace designed for flow—generate, iterate, and ship faster than ever before.
                  </p>
                </div>
                <div className="grid gap-6">
                  {[
                    {
                      title: "Intuitive design that gets out of your way",
                      pattern: <div className="absolute top-3 right-3 flex space-x-1">
                        <div className="w-1 h-1 bg-zinc-500/40 rounded-full" />
                        <div className="w-1 h-1 bg-zinc-500/30 rounded-full" />
                        <div className="w-1 h-1 bg-zinc-500/20 rounded-full" />
                      </div>
                    },
                    {
                      title: "Lightning-fast responses and interactions",
                      pattern: <div className="absolute top-3 right-3 w-6 h-6 border border-zinc-600/30 rounded-full" />
                    },
                    {
                      title: "Advanced features for complex workflows",
                      pattern: <div className="absolute top-3 right-3">
                        <div className="w-4 h-px bg-zinc-500/40 mb-1" />
                        <div className="w-3 h-px bg-zinc-500/30 mb-1" />
                        <div className="w-2 h-px bg-zinc-500/20" />
                      </div>
                    }
                  ].map((item, idx) => (
                    <div key={idx} className="relative bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 rounded-xl p-6 transition-all duration-300 hover:border-zinc-700/60 hover:bg-gradient-to-r hover:from-zinc-900/60 hover:to-zinc-800/40">
                      {item.pattern}
                      <div className="flex items-center space-x-4">
                        <div className="w-1.5 h-1.5 bg-zinc-500 rounded-full" />
                        <span className="text-gray-300 text-lg font-medium">{item.title}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Trusted by Section */}
        <section className="py-20 bg-black relative overflow-hidden">
          {/* Sophisticated Background Effects */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-10 left-1/4 w-80 h-80 bg-gradient-to-br from-zinc-800/15 to-transparent rounded-full blur-3xl" />
            <div className="absolute bottom-10 right-1/4 w-96 h-96 bg-gradient-to-tl from-slate-800/20 to-transparent rounded-full blur-3xl" />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-gradient-to-br from-gray-900/10 to-transparent rounded-full blur-[150px]" />
          </div>
          
          <div className="container mx-auto text-center relative z-10">
            <div className="mb-16">
              <p className="text-xs uppercase tracking-[0.2em] text-zinc-500 mb-3 font-medium">Trusted by innovators at</p>
              <div className="w-24 h-px bg-gradient-to-r from-transparent via-zinc-600 to-transparent mx-auto" />
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 max-w-5xl mx-auto">
              {[
                { name: "Microsoft", weight: "font-light" },
                { name: "Google", weight: "font-medium" },
                { name: "Meta", weight: "font-normal" },
                { name: "OpenAI", weight: "font-medium" },
                { name: "Anthropic", weight: "font-light" }
              ].map((company, idx) => (
                <div key={company.name} className="group relative cursor-pointer">
                  {/* Subtle hover background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-zinc-900/30 to-zinc-800/10 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-500 blur-sm" />
                  
                  {/* Company card */}
                  <div className="relative bg-gradient-to-br from-zinc-900/40 via-slate-900/20 to-zinc-900/40 border border-zinc-800/30 rounded-xl p-6 transition-all duration-300 hover:border-zinc-700/50 hover:bg-gradient-to-br hover:from-zinc-900/60 hover:via-slate-900/40 hover:to-zinc-900/60 backdrop-blur-sm group-hover:transform group-hover:scale-[1.02] group-hover:shadow-xl">
                    {/* Subtle pattern overlay */}
                    <div className="absolute inset-0 opacity-5">
                      <div className={`absolute ${
                        idx === 0 ? 'top-3 right-3 w-6 h-6 border border-white/20 rounded-full' :
                        idx === 1 ? 'top-3 right-3 w-4 h-4 bg-white/10 rounded' :
                        idx === 2 ? 'top-3 right-3 w-8 h-px bg-white/20' :
                        idx === 3 ? 'top-3 right-3 w-5 h-5 border border-white/15 rotate-45' :
                        'top-3 right-3 w-2 h-2 bg-white/20 rounded-full'
                      }`} />
                    </div>
                    
                    <div className="relative z-10 text-center">
                      {/* Status indicator */}
                      <div className="flex justify-center mb-3">
                        <div className="w-1.5 h-1.5 bg-zinc-500/60 rounded-full group-hover:bg-zinc-400/80 transition-colors duration-300" />
                      </div>
                      
                      {/* Company name */}
                      <h3 className={`text-lg ${company.weight} text-gray-300 group-hover:text-white transition-colors duration-300 tracking-tight`}>
                        {company.name}
                      </h3>
                      
                      {/* Subtle underline on hover */}
                      <div className="mt-2 h-px bg-gradient-to-r from-transparent via-zinc-600/0 to-transparent group-hover:via-zinc-600/60 transition-all duration-500 mx-4" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Bottom accent */}
            <div className="mt-16 flex justify-center">
              <div className="flex items-center space-x-2">
                <div className="w-1 h-1 bg-zinc-600/40 rounded-full animate-pulse" />
                <div className="w-16 h-px bg-gradient-to-r from-zinc-600/0 via-zinc-600/60 to-zinc-600/0" />
                <div className="w-1 h-1 bg-zinc-600/40 rounded-full animate-pulse animation-delay-1000" />
              </div>
            </div>
            
            {/* Optional stats or additional text */}
            <div className="mt-8">
              <p className="text-xs text-zinc-600 font-medium tracking-wide">
                Join thousands of teams building the future with AI
              </p>
            </div>
+          </div>
         </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-24 px-4 bg-black relative overflow-hidden">
          <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-900/20 via-transparent to-blue-900/20 blur-[100px] -z-10 opacity-30" />
          <div className="container mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6">
                Choose Your Plan
              </h2>
              <p className="text-xl text-gray-400 max-w-2xl mx-auto mb-8">
                Start free and scale as you grow. All plans include access to our powerful AI models.
              </p>
            
              <div className="flex justify-center items-center mb-12">
                <div className="flex items-center bg-white/[.05] rounded-full p-1 border border-white/10">
                  <button
                    onClick={() => setIsAnnual(false)}
                    className={cn(
                      "px-6 py-2 rounded-full text-sm font-medium transition-all duration-300",
                      !isAnnual 
                        ? "bg-white text-black shadow-lg" 
                        : "text-gray-400 hover:text-white"
                    )}
                  >
                    Monthly
                  </button>
                  <button
                    onClick={() => setIsAnnual(true)}
                    className={cn(
                      "px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 relative",
                      isAnnual 
                        ? "bg-white text-black shadow-lg" 
                        : "text-gray-400 hover:text-white"
                    )}
                  >
                    Yearly
                    <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs px-2 py-0.5 rounded-full">
                      -20%
                    </span>
                  </button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 max-w-7xl mx-auto">
              {pricingPlans.map((plan, index) => (
                <div 
                  key={index} 
                  className={`relative p-6 border rounded-2xl bg-white/[.03] backdrop-blur-sm transition-all duration-300 hover:bg-white/[.06] hover:-translate-y-1 flex flex-col ${
                    plan.popular ? 'border-blue-500/50 ring-1 ring-blue-500/30 shadow-lg shadow-blue-500/20' : 'border-white/10 hover:border-white/20'
                  } ${!plan.available ? 'opacity-75' : ''}`}
                >
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-semibold px-3 py-1 rounded-full">
                      Most Popular
                      </div>
                    </div>
                  )}
                  
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                    <div className="mb-4">
                      <span className="text-3xl font-bold text-white">
                        {plan.name === "Free" ? plan.price : (isAnnual ? `$${Math.round(parseInt(plan.annualPrice.replace('$', '').replace(',', '')) / 12)}` : plan.price)}
                      </span>
                      <span className="text-gray-400 text-sm">
                        /{plan.name === "Free" ? plan.period : "month"}
                      </span>
                    </div>
                    {isAnnual && plan.name !== "Free" && (
                      <p className="text-xs text-gray-500 mb-4">
                        Billed as {plan.annualPrice} per year
                      </p>
                    )}
                    <p className="text-sm text-gray-400 mb-6 h-12 flex items-center justify-center">
                      {plan.description}
                    </p>
                  </div>
                  
                  <div className="space-y-4 mb-6 flex-grow">
                    {plan.features.slice(0, 4).map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-2 text-sm">
                        <Check className="h-4 w-4 text-blue-400 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-400 leading-relaxed">{feature}</span>
                        </div>
                      ))}
                    {plan.features.length > 4 && (
                      <div className="text-xs text-gray-500 text-center pt-2">
                        +{plan.features.length - 4} more features
                      </div>
                    )}
                    </div>
                    
                  <div className="mt-auto">
                      <Button 
                      className={`w-full ${plan.popular ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg' : 'bg-white/10 hover:bg-white/20 text-white border border-white/20'}`}
                        onClick={plan.available ? handleGetStarted : undefined}
                        disabled={!plan.available}
                      >
                        {!plan.available && <Clock className="h-4 w-4 mr-2" />}
                        {plan.buttonText}
                      </Button>
                    
                    {!plan.available && (
                      <p className="text-xs text-gray-500 text-center pt-3">
                        Get notified when available
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-12">
              <p className="text-sm text-gray-500 leading-relaxed">
                All plans include secure conversations, multiple AI models, and real-time streaming.
                <br />
                No hidden fees. Cancel anytime. Enterprise plans available upon request.
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="py-24 px-4 bg-black relative overflow-hidden">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[50rem] h-[50rem] bg-gradient-to-br from-blue-900/5 to-purple-900/5 rounded-full blur-[200px] -z-10 opacity-40" />
          
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-4">
              Frequently Asked Questions
            </h2>
              <p className="text-lg text-gray-400 max-w-2xl mx-auto">
                Everything you need to know about ErzenAI plans, credits, and features.
              </p>
            </div>

            <Accordion type="single" collapsible className="grid gap-4">
              {faqs.map((item, idx) => (
                <AccordionItem 
                  key={`item-${idx}`}
                  value={`item-${idx}`} 
                  className="rounded-2xl border border-white/10 bg-white/[.03] backdrop-blur-sm hover:bg-white/[.06] transition-all duration-300 overflow-hidden"
                >
                  <AccordionTrigger className="text-left font-semibold text-white hover:no-underline px-6 py-5 text-base md:text-lg hover:text-blue-300 transition-colors">
                    {item.q}
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-5">
                    <div className="pt-2 border-t border-white/10">
                      <p className="text-gray-300 leading-relaxed">{item.a}</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>

            <div className="text-center mt-12">
              <p className="text-gray-500 text-sm">
                Still have questions? <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors">Contact our support team</a>
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-32 px-4 relative overflow-hidden">
          {/* Epic Background Effects */}
          <div className="absolute inset-0 bg-black">
            {/* Animated Gradient Orbs */}
            <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-600/40 to-purple-600/40 rounded-full blur-3xl animate-pulse opacity-60" />
            <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-gradient-to-l from-cyan-500/30 to-blue-600/30 rounded-full blur-3xl animate-pulse animation-delay-2000 opacity-50" />
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-br from-purple-600/20 via-blue-600/20 to-cyan-500/20 rounded-full blur-[200px] animate-spin-slow opacity-40" />
            
            {/* Floating Particles */}
            <div className="absolute top-20 left-10 w-2 h-2 bg-blue-400 rounded-full animate-bounce animation-delay-1000 opacity-70" />
            <div className="absolute top-40 right-20 w-1 h-1 bg-purple-400 rounded-full animate-bounce animation-delay-3000 opacity-60" />
            <div className="absolute bottom-32 left-1/3 w-1.5 h-1.5 bg-cyan-400 rounded-full animate-bounce animation-delay-2000 opacity-80" />
            <div className="absolute top-60 right-1/3 w-1 h-1 bg-blue-300 rounded-full animate-bounce animation-delay-4000 opacity-50" />
            
            {/* Grid Pattern Overlay */}
            <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.02)_1px,transparent_1px)] bg-[size:100px_100px] opacity-20" />
          </div>

          <div className="container mx-auto relative z-10 max-w-4xl">
            <div className="text-center">
              {/* Clean Title */}
              <h2 className="text-4xl md:text-5xl font-extrabold text-white mb-6 leading-tight">
                Unlock Your AI Edge Instantly
              </h2>
              
              {/* Clean Subtitle */}
              <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed">
                Join the next generation of creators, teams, and innovators using ErzenAI to turn ideas into action—faster than ever before. Sign up and start building with AI in seconds.
              </p>

              {/* Clean Button */}
              <div className="mb-8">
                <Button 
                  size="lg" 
                  onClick={handleGetStarted} 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 rounded-xl text-lg font-semibold transform transition-all duration-300 hover:scale-105 shadow-xl border-0"
                >
                  Get Started with ErzenAI
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
              </div>
              
              {/* Clean Features */}
              <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Sign up in seconds</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>No credit card needed</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Access 75+ AI models</span>
                </div>
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Instant project memory</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Epic Footer */}
        <footer className="relative bg-black overflow-hidden">
          {/* Epic Background */}
          <div className="absolute inset-0">
            {/* Gradient Mesh */}
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-900/10 via-purple-900/10 to-black opacity-80" />
            {/* Animated Lines */}
            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent animate-aurora" />
            <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-500 to-transparent animate-aurora animation-delay-2000" />
            {/* Floating Orbs */}
            <div className="absolute top-10 right-20 w-32 h-32 bg-blue-600/20 rounded-full blur-2xl animate-float" />
            <div className="absolute bottom-20 left-10 w-24 h-24 bg-purple-600/20 rounded-full blur-2xl animate-float animation-delay-3000" />
            </div>

          <div className="relative z-10 border-t border-white/10 backdrop-blur-sm">
            <div className="container mx-auto px-4 py-16">
              <div className="flex flex-col md:flex-row justify-between items-start gap-12 md:gap-8">
                {/* Branding */}
                <div className="flex flex-col items-start space-y-2 md:w-1/4">
                <div className="flex items-center space-x-4 group">
                    <img src="/icon0.svg" alt="ErzenAI" className="h-12 w-12 rounded-lg transform group-hover:scale-110 transition-transform duration-300" />
                    <span className="text-3xl font-black bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent">
                      ErzenAI
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 tracking-wider mt-2">
                      THE FUTURE OF AI
                    </div>
                  </div>
                {/* Footer Navigation Groups */}
                <div className="flex flex-wrap gap-12 md:gap-16 flex-1 justify-between">
                  {navigationGroups.map((group) => (
                    <div key={group.label} className="min-w-[120px]">
                      <div className="font-semibold text-gray-400 uppercase text-xs mb-3">{group.label}</div>
                      <ul className="space-y-2">
                        {group.items.map((item) => (
                          <li key={item.title}>
                            <a
                              href={item.href}
                              className="text-gray-300 hover:text-white transition-colors text-sm"
                            >
                              {item.title}
                            </a>
                          </li>
                        ))}
                      </ul>
              </div>
                  ))}
              </div>
              </div>
              {/* Copyright */}
              <div className="text-center mt-12 pt-8 border-t border-white/5">
                <p className="text-sm text-gray-500 relative">
                  <span className="relative z-10">
                    © 2025 ErzenAI. All rights reserved.
                  </span>
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded blur-sm opacity-0 hover:opacity-100 transition-opacity duration-1000" />
            </p>
          </div>
      </div>
          </div>
        </footer>
      </main>
    </div>
  );
} 