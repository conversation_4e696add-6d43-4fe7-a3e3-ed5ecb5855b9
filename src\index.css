@import "tailwindcss";

@custom-variant dark (&:is(.dark *));
@config "../tailwind.config.cjs";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 1rem;
    --body-font-size: 1.125rem;
  }

  .dark {
    /* Increase overall foreground brightness slightly for readability */
    --background: 224 60% 5%;
    --foreground: 213 28% 94%;

    --card: 224 60% 5.5%;
    --card-foreground: 213 28% 94%;

    --popover: 224 60% 5.5%;
    --popover-foreground: 213 28% 94%;

    /* Keep primary near-white for legible text/iconography on primary backgrounds */
    --primary: 210 40% 96%;
    --primary-foreground: 210 40% 10%;

    /* Lift secondary/muted surfaces a touch to separate layers */
    --secondary: 215 22% 18%;
    --secondary-foreground: 210 40% 96%;

    --muted: 215 22% 16%;
    --muted-foreground: 218 12% 72%;

    --accent: 215 22% 18%;
    --accent-foreground: 210 40% 96%;

    --destructive: 0 62.8% 40%;
    --destructive-foreground: 210 40% 98%;

    /* Crisper borders/inputs for clearer separation on dark */
    --border: 215 18% 26%;
    --input: 215 18% 26%;
    --ring: 216 30% 24%;
    --body-font-size: 1.125rem;
  }

  .theme-blue {
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 0%;
    --ring: 217 91% 60%;
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 0%;
    --secondary: 217 30% 96%;
    --secondary-foreground: 217 91% 25%;
    --muted: 217 30% 94%;
    --muted-foreground: 217 30% 40%;
    --card: 217 30% 99%;
    --card-foreground: 217 91% 10%;
    --popover: 217 30% 99%;
    --popover-foreground: 217 91% 10%;
    --border: 217 30% 90%;
    --input: 217 30% 90%;
  }

  .dark.theme-blue {
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 217 91% 60%;
    --accent: 217 45% 20%;
    --accent-foreground: 217 91% 80%;
    --secondary: 217 30% 15%;
    --secondary-foreground: 217 91% 80%;
    --muted: 217 30% 12%;
    --muted-foreground: 217 30% 65%;
    --card: 217 45% 6%;
    --card-foreground: 217 91% 85%;
    --popover: 217 45% 6%;
    --popover-foreground: 217 91% 85%;
    --border: 217 30% 20%;
    --input: 217 30% 20%;
  }

  .theme-green {
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 0%;
    --ring: 142 71% 45%;
    --accent: 142 71% 45%;
    --accent-foreground: 0 0% 0%;
    --secondary: 142 30% 96%;
    --secondary-foreground: 142 71% 25%;
    --muted: 142 30% 94%;
    --muted-foreground: 142 30% 40%;
    --card: 142 30% 99%;
    --card-foreground: 142 71% 10%;
    --popover: 142 30% 99%;
    --popover-foreground: 142 71% 10%;
    --border: 142 30% 90%;
    --input: 142 30% 90%;
  }

  .dark.theme-green {
    --primary: 142 71% 45%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 142 71% 45%;
    --accent: 142 45% 20%;
    --accent-foreground: 142 71% 80%;
    --secondary: 142 30% 15%;
    --secondary-foreground: 142 71% 80%;
    --muted: 142 30% 12%;
    --muted-foreground: 142 30% 65%;
    --card: 142 45% 6%;
    --card-foreground: 142 71% 85%;
    --popover: 142 45% 6%;
    --popover-foreground: 142 71% 85%;
    --border: 142 30% 20%;
    --input: 142 30% 20%;
  }

  .theme-purple {
    --primary: 263 85% 70%;
    --primary-foreground: 0 0% 0%;
    --ring: 263 85% 70%;
    --accent: 263 85% 70%;
    --accent-foreground: 0 0% 0%;
    --secondary: 263 30% 96%;
    --secondary-foreground: 263 85% 35%;
    --muted: 263 30% 94%;
    --muted-foreground: 263 30% 40%;
    --card: 263 30% 99%;
    --card-foreground: 263 85% 10%;
    --popover: 263 30% 99%;
    --popover-foreground: 263 85% 10%;
    --border: 263 30% 90%;
    --input: 263 30% 90%;
  }

  .dark.theme-purple {
    --primary: 263 85% 70%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 263 85% 70%;
    --accent: 263 45% 20%;
    --accent-foreground: 263 85% 80%;
    --secondary: 263 30% 15%;
    --secondary-foreground: 263 85% 80%;
    --muted: 263 30% 12%;
    --muted-foreground: 263 30% 65%;
    --card: 263 45% 6%;
    --card-foreground: 263 85% 85%;
    --popover: 263 45% 6%;
    --popover-foreground: 263 85% 85%;
    --border: 263 30% 20%;
    --input: 263 30% 20%;
  }

  .theme-orange {
    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 0%;
    --ring: 25 95% 53%;
    --accent: 25 95% 53%;
    --accent-foreground: 0 0% 0%;
    --secondary: 25 30% 96%;
    --secondary-foreground: 25 95% 25%;
    --muted: 25 30% 94%;
    --muted-foreground: 25 30% 40%;
    --card: 25 30% 99%;
    --card-foreground: 25 95% 10%;
    --popover: 25 30% 99%;
    --popover-foreground: 25 95% 10%;
    --border: 25 30% 90%;
    --input: 25 30% 90%;
  }

  .dark.theme-orange {
    --primary: 25 95% 53%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 25 95% 53%;
    --accent: 25 45% 20%;
    --accent-foreground: 25 95% 80%;
    --secondary: 25 30% 15%;
    --secondary-foreground: 25 95% 80%;
    --muted: 25 30% 12%;
    --muted-foreground: 25 30% 65%;
    --card: 25 45% 6%;
    --card-foreground: 25 95% 85%;
    --popover: 25 45% 6%;
    --popover-foreground: 25 95% 85%;
    --border: 25 30% 20%;
    --input: 25 30% 20%;
  }

  .theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 0%;
    --ring: 330 81% 60%;
    --accent: 330 81% 60%;
    --accent-foreground: 0 0% 0%;
    --secondary: 330 30% 96%;
    --secondary-foreground: 330 81% 30%;
    --muted: 330 30% 94%;
    --muted-foreground: 330 30% 40%;
    --card: 330 30% 99%;
    --card-foreground: 330 81% 10%;
    --popover: 330 30% 99%;
    --popover-foreground: 330 81% 10%;
    --border: 330 30% 90%;
    --input: 330 30% 90%;
  }

  .dark.theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 330 81% 60%;
    --accent: 330 45% 20%;
    --accent-foreground: 330 81% 80%;
    --secondary: 330 30% 15%;
    --secondary-foreground: 330 81% 80%;
    --muted: 330 30% 12%;
    --muted-foreground: 330 30% 65%;
    --card: 330 45% 6%;
    --card-foreground: 330 81% 85%;
    --popover: 330 45% 6%;
    --popover-foreground: 330 81% 85%;
    --border: 330 30% 20%;
    --input: 330 30% 20%;
  }

  .theme-teal {
    --primary: 173 80% 40%;
    --primary-foreground: 0 0% 0%;
    --ring: 173 80% 40%;
    --accent: 173 80% 40%;
    --accent-foreground: 0 0% 0%;
    --secondary: 173 30% 96%;
    --secondary-foreground: 173 80% 20%;
    --muted: 173 30% 94%;
    --muted-foreground: 173 30% 40%;
    --card: 173 30% 99%;
    --card-foreground: 173 80% 10%;
    --popover: 173 30% 99%;
    --popover-foreground: 173 80% 10%;
    --border: 173 30% 90%;
    --input: 173 30% 90%;
  }

  .dark.theme-teal {
    --primary: 173 80% 40%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 173 80% 40%;
    --accent: 173 45% 20%;
    --accent-foreground: 173 80% 80%;
    --secondary: 173 30% 15%;
    --secondary-foreground: 173 80% 80%;
    --muted: 173 30% 12%;
    --muted-foreground: 173 30% 65%;
    --card: 173 45% 6%;
    --card-foreground: 173 80% 85%;
    --popover: 173 45% 6%;
    --popover-foreground: 173 80% 85%;
    --border: 173 30% 20%;
    --input: 173 30% 20%;
  }

  .theme-red {
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 0%;
    --ring: 0 84% 60%;
    --accent: 0 84% 60%;
    --accent-foreground: 0 0% 0%;
    --secondary: 0 30% 96%;
    --secondary-foreground: 0 84% 30%;
    --muted: 0 30% 94%;
    --muted-foreground: 0 30% 40%;
    --card: 0 30% 99%;
    --card-foreground: 0 84% 10%;
    --popover: 0 30% 99%;
    --popover-foreground: 0 84% 10%;
    --border: 0 30% 90%;
    --input: 0 30% 90%;
  }

  .dark.theme-red {
    --primary: 0 84% 60%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 0 84% 60%;
    --accent: 0 45% 20%;
    --accent-foreground: 0 84% 80%;
    --secondary: 0 30% 15%;
    --secondary-foreground: 0 84% 80%;
    --muted: 0 30% 12%;
    --muted-foreground: 0 30% 65%;
    --card: 0 45% 6%;
    --card-foreground: 0 84% 85%;
    --popover: 0 45% 6%;
    --popover-foreground: 0 84% 85%;
    --border: 0 30% 20%;
    --input: 0 30% 20%;
  }

  .theme-indigo {
    --primary: 231 48% 48%;
    --primary-foreground: 0 0% 0%;
    --ring: 231 48% 48%;
    --accent: 231 48% 48%;
    --accent-foreground: 0 0% 0%;
    --secondary: 231 30% 96%;
    --secondary-foreground: 231 48% 24%;
    --muted: 231 30% 94%;
    --muted-foreground: 231 30% 40%;
    --card: 231 30% 99%;
    --card-foreground: 231 48% 10%;
    --popover: 231 30% 99%;
    --popover-foreground: 231 48% 10%;
    --border: 231 30% 90%;
    --input: 231 30% 90%;
  }

  .dark.theme-indigo {
    --primary: 231 48% 48%;
    --primary-foreground: 0 0% 0%;  /* Changed to black for better contrast */
    --ring: 231 48% 48%;
    --accent: 231 45% 20%;
    --accent-foreground: 231 48% 80%;
    --secondary: 231 30% 15%;
    --secondary-foreground: 231 48% 80%;
    --muted: 231 30% 12%;
    --muted-foreground: 231 30% 65%;
    --card: 231 45% 6%;
    --card-foreground: 231 48% 85%;
    --popover: 231 45% 6%;
    --popover-foreground: 231 48% 85%;
    --border: 231 30% 20%;
    --input: 231 30% 20%;
  }

  .voice-outline::before {
    content: "";
    position: fixed;
    inset: 0;
    padding: 3px; /* Siri-like outline thickness */
    border-radius: 0.5rem;
    background: conic-gradient(
      from 0deg at 50% 50%,
      #FF2D55, /* Pink */
      #FF9500, /* Orange */
      #FFCC00, /* Yellow */
      #34C759, /* Green */
      #007AFF, /* Blue */
      #5856D6, /* Purple */
      #FF2D55  /* Pink again */
    );
    z-index: 100;
    pointer-events: none;
    /* Create a hollow effect so the gradient shows as a border */
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
    animation: siriColorShift 3s linear infinite;
  }

  @keyframes siriColorShift {
    0% {
      background: conic-gradient(
        from 0deg at 50% 50%,
        #FF2D55, /* Pink */
        #FF9500, /* Orange */
        #FFCC00, /* Yellow */
        #34C759, /* Green */
        #007AFF, /* Blue */
        #5856D6, /* Purple */
        #FF2D55  /* Pink again */
      );
    }
    100% {
      background: conic-gradient(
        from 360deg at 50% 50%,
        #FF2D55, /* Pink */
        #FF9500, /* Orange */
        #FFCC00, /* Yellow */
        #34C759, /* Green */
        #007AFF, /* Blue */
        #5856D6, /* Purple */
        #FF2D55  /* Pink again */
      );
    }
  }

  /* Prebuilt Extra Themes */
  .theme-sunset {
    --primary: 14 90% 57%;
    --primary-foreground: 0 0% 0%;
    --ring: 14 90% 57%;
    --accent: 14 90% 57%;
    --accent-foreground: 0 0% 0%;
    --secondary: 20 30% 96%;
    --secondary-foreground: 14 90% 30%;
    --muted: 20 30% 94%;
    --muted-foreground: 20 30% 40%;
    --card: 20 30% 99%;
    --card-foreground: 14 90% 10%;
    --popover: 20 30% 99%;
    --popover-foreground: 14 90% 10%;
    --border: 20 30% 90%;
    --input: 20 30% 90%;
  }

  .dark.theme-sunset {
    --primary: 14 90% 57%;
    --primary-foreground: 0 0% 0%;
    --ring: 14 90% 57%;
    --accent: 14 60% 20%;
    --accent-foreground: 14 90% 80%;
    --secondary: 20 30% 15%;
    --secondary-foreground: 14 90% 80%;
    --muted: 20 30% 12%;
    --muted-foreground: 20 30% 65%;
    --card: 14 50% 6%;
    --card-foreground: 14 90% 85%;
    --popover: 14 50% 6%;
    --popover-foreground: 14 90% 85%;
    --border: 20 30% 20%;
    --input: 20 30% 20%;
  }

  .theme-ocean {
    --primary: 195 85% 45%;
    --primary-foreground: 0 0% 0%;
    --ring: 195 85% 45%;
    --accent: 195 85% 45%;
    --accent-foreground: 0 0% 0%;
    --secondary: 195 30% 96%;
    --secondary-foreground: 195 85% 25%;
    --muted: 195 30% 94%;
    --muted-foreground: 195 30% 40%;
    --card: 195 30% 99%;
    --card-foreground: 195 85% 10%;
    --popover: 195 30% 99%;
    --popover-foreground: 195 85% 10%;
    --border: 195 30% 90%;
    --input: 195 30% 90%;
  }

  .dark.theme-ocean {
    --primary: 195 85% 45%;
    --primary-foreground: 0 0% 0%;
    --ring: 195 85% 45%;
    --accent: 195 60% 18%;
    --accent-foreground: 195 85% 80%;
    --secondary: 195 30% 15%;
    --secondary-foreground: 195 85% 80%;
    --muted: 195 30% 12%;
    --muted-foreground: 195 30% 65%;
    --card: 195 45% 6%;
    --card-foreground: 195 85% 85%;
    --popover: 195 45% 6%;
    --popover-foreground: 195 85% 85%;
    --border: 195 30% 20%;
    --input: 195 30% 20%;
  }

  .theme-forest {
    --primary: 140 55% 40%;
    --primary-foreground: 0 0% 0%;
    --ring: 140 55% 40%;
    --accent: 140 55% 40%;
    --accent-foreground: 0 0% 0%;
    --secondary: 140 20% 96%;
    --secondary-foreground: 140 55% 25%;
    --muted: 140 20% 94%;
    --muted-foreground: 140 20% 35%;
    --card: 140 20% 99%;
    --card-foreground: 140 55% 10%;
    --popover: 140 20% 99%;
    --popover-foreground: 140 55% 10%;
    --border: 140 20% 90%;
    --input: 140 20% 90%;
  }

  .dark.theme-forest {
    --primary: 140 55% 40%;
    --primary-foreground: 0 0% 0%;
    --ring: 140 55% 40%;
    --accent: 140 35% 18%;
    --accent-foreground: 140 55% 80%;
    --secondary: 140 20% 15%;
    --secondary-foreground: 140 55% 80%;
    --muted: 140 20% 12%;
    --muted-foreground: 140 20% 65%;
    --card: 140 35% 6%;
    --card-foreground: 140 55% 85%;
    --popover: 140 35% 6%;
    --popover-foreground: 140 55% 85%;
    --border: 140 20% 20%;
    --input: 140 20% 20%;
  }

  .theme-gold {
    --primary: 45 85% 55%;
    --primary-foreground: 0 0% 0%;
    --ring: 45 85% 55%;
    --accent: 45 85% 55%;
    --accent-foreground: 0 0% 0%;
    --secondary: 45 30% 96%;
    --secondary-foreground: 45 85% 25%;
    --muted: 45 30% 94%;
    --muted-foreground: 45 30% 40%;
    --card: 45 30% 99%;
    --card-foreground: 45 85% 10%;
    --popover: 45 30% 99%;
    --popover-foreground: 45 85% 10%;
    --border: 45 30% 90%;
    --input: 45 30% 90%;
  }

  .dark.theme-gold {
    --primary: 45 85% 55%;
    --primary-foreground: 0 0% 0%;
    --ring: 45 85% 55%;
    --accent: 45 50% 20%;
    --accent-foreground: 45 85% 80%;
    --secondary: 45 30% 15%;
    --secondary-foreground: 45 85% 80%;
    --muted: 45 30% 12%;
    --muted-foreground: 45 30% 65%;
    --card: 45 45% 6%;
    --card-foreground: 45 85% 85%;
    --popover: 45 45% 6%;
    --popover-foreground: 45 85% 85%;
    --border: 45 30% 20%;
    --input: 45 30% 20%;
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Beautiful text selection styling */
  ::selection {
    background: color-mix(in oklch, var(--primary) 40%, transparent);
    color: var(--primary-foreground);
    text-shadow: none;
  }

  ::-moz-selection {
    background: color-mix(in oklch, var(--primary) 40%, transparent);
    color: var(--primary-foreground);
    text-shadow: none;
  }

  /* Enhanced selection for code blocks and special elements */
  code ::selection,
  pre ::selection,
  .prose ::selection {
    background: color-mix(in oklch, var(--primary) 50%, transparent);
    color: var(--primary-foreground);
    text-shadow: none;
  }

  code ::-moz-selection,
  pre ::-moz-selection,
  .prose ::-moz-selection {
    background: color-mix(in oklch, var(--primary) 50%, transparent);
    color: var(--primary-foreground);
    text-shadow: none;
  }

  /* Dark mode adjustments */
  .dark ::selection {
    background: color-mix(in oklch, var(--primary) 25%, transparent);
    color: var(--background);
  }

  .dark ::-moz-selection {
    background: color-mix(in oklch, var(--primary) 25%, transparent);
    color: var(--background);
  }

  .dark code ::selection,
  .dark pre ::selection,
  .dark .prose ::selection {
    background: color-mix(in oklch, var(--primary) 35%, transparent);
    color: var(--background);
  }

  .dark code ::-moz-selection,
  .dark pre ::-moz-selection,
  .dark .prose ::-moz-selection {
    background: color-mix(in oklch, var(--primary) 35%, transparent);
    color: var(--background);
  }

  /* Ensure cursor pointer on interactive elements */
  button, 
  [role="button"],
  a[href],
  .btn-primary,
  .btn-secondary,
  .btn-accent,
  .auth-button {
    @apply cursor-pointer;
  }

  .markdown-content a {
    @apply cursor-pointer transition-colors;
  }

  /* Enhanced focus states for accessibility */
  button:focus,
  [role="button"]:focus,
  a[href]:focus,
  input:focus,
  textarea:focus,
  .focus-ring:focus {
    outline: none;
    ring: 2px;
    ring-color: hsl(var(--primary));
    ring-offset: 2px;
    ring-offset-color: hsl(var(--background));
    transition: all 0.2s ease;
  }
}

.accent-text {
  @apply text-slate-600;
}

/* Apple-inspired Typography System */
:root {
  /* Typography Scale - Based on 16px base with 1.25 ratio (Major Third) */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  --font-size-7xl: 4.5rem;    /* 72px */
  --font-size-8xl: 6rem;      /* 96px */
  --font-size-9xl: 8rem;      /* 128px */

  /* Line Heights - Optimized for readability */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Font Weights - Semantic naming */
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Letter Spacing - Apple-style */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0rem;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
  --background: oklch(0.9900 0 0);  /* Pure white like Apple */
  --foreground: oklch(0.1000 0 0);  /* Pure black text */
  --card: oklch(1.0000 0 0);  /* Pure white cards */
  --card-foreground: oklch(0.1000 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1000 0 0);
  --primary: oklch(0.1000 0 0);  /* Pure black primary */
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9600 0 0);  /* Clean light gray */
  --secondary-foreground: oklch(0.1000 0 0);
  --muted: oklch(0.9600 0 0);  /* Clean gray backgrounds */
  --muted-foreground: oklch(0.4000 0 0);  /* Clean medium gray */
  --accent: oklch(0.9400 0 0);  /* Clean accent gray */
  --accent-foreground: oklch(0.1000 0 0);
  --destructive: oklch(0.5000 0.2000 30);  /* Apple red */
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9000 0 0);  /* Clean light border */
  --input: oklch(1.0000 0 0);  /* Pure white inputs */
  --ring: oklch(0.4000 0 0);
  --chart-1: oklch(0.6231 0.1880 259.8145);
  --chart-2: oklch(0.6268 0.2325 303.9004);
  --chart-3: oklch(0.7686 0.1647 70.0804);
  --chart-4: oklch(0.6959 0.1491 162.4796);
  --chart-5: oklch(0.6368 0.2078 25.3313);
  --radius: 0.75rem;
  --sidebar: oklch(0.9700 0 0);  /* Clean Apple light gray */
  --sidebar-foreground: oklch(0.3000 0 0);
  --sidebar-primary: oklch(0.1000 0 0);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9400 0 0);
  --sidebar-accent-foreground: oklch(0.1000 0 0);
  --sidebar-border: oklch(0.9000 0 0);
  --sidebar-ring: oklch(0.4000 0 0);
  --font-sans: Inter;
  --font-serif: Georgia;
  --font-mono: JetBrains Mono;
  --shadow-color: #000000;
  --shadow-opacity: 0.1;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0rem;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

/* Enhanced Font Stack - System fonts first for Apple devices */
body {
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "SF Pro Display",
    "SF Pro Text",
    "Helvetica Neue",
    Helvetica,
    Arial,
    "Segoe UI",
    "Roboto",
    "Oxygen",
    "Ubuntu",
    "Cantarell",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol";

  /* Base typography settings */
  font-size: var(--font-size-base);
  line-height: var(--leading-normal);
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-normal);

  /* Enhanced text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1, "clig" 1, "calt" 1;
}

/* only use this to update the style of the auth input fields. use a different class for all other input fields */
.auth-input-field {
  @apply w-full px-4 py-3 rounded-lg bg-white border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-shadow shadow-sm hover:shadow;
}

/* only use this to update the style of the auth buttons. use the button class for all other buttons */
.auth-button {
  @apply w-full px-4 py-3 rounded bg-primary text-primary-foreground font-semibold hover:bg-primary/90 transition-colors shadow-sm hover:shadow disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Enhanced theme-aware scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted)) hsl(var(--background));
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

*::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Theme-aware focus rings */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background;
}

/* Enhanced card styling */
.themed-card {
  @apply bg-card text-card-foreground border border-border/50 shadow-sm transition-all duration-200 hover:shadow-md;
}

/* Enhanced button variants */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90 border border-primary transition-all duration-200 hover:shadow-md active:scale-95;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border transition-all duration-200 hover:shadow-md active:scale-95;
}

.btn-accent {
  @apply bg-accent text-accent-foreground hover:bg-accent/80 border border-border transition-all duration-200 hover:shadow-md active:scale-95;
}

/* Enhanced input styling */
.themed-input {
  background-color: hsl(var(--input));
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  @apply transition-all duration-200 focus:shadow-md;
}

/* Perfect Markdown Content Styles - Apple HIG Inspired */
.markdown-content {
  @apply text-foreground;

  /* Base typography using our perfect system */
  font-size: var(--font-size-base);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-normal);

  /* Enhanced readability */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* List styles - Improved reliability and consistency */
.markdown-content ul {
  list-style-type: disc;
  list-style-position: outside;
  padding-left: 2rem;
  margin: 1rem 0;
}

.markdown-content ol {
  list-style-type: decimal;
  list-style-position: outside;
  padding-left: 2rem;
  margin: 1rem 0;
}

.markdown-content li {
  line-height: 1.6;
  margin-bottom: 0.5rem;
  padding-left: 0.25rem;
}

.markdown-content li::marker {
  color: var(--primary);
  font-weight: 500;
}

/* Nested lists with proper indentation and marker types */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.markdown-content ul ul {
  list-style-type: circle;
}

.markdown-content ul ul ul {
  list-style-type: square;
}

.markdown-content ol ol {
  list-style-type: lower-alpha;
}

.markdown-content ol ol ol {
  list-style-type: lower-roman;
}

/* Tight lists (no spacing between items) */
.markdown-content .tight li {
  margin-bottom: 0;
}

.markdown-content .tight li p {
  margin-bottom: 0;
}

/* Task lists (checkboxes) - Better positioning and styling */
.markdown-content input[type="checkbox"] {
  margin-right: 0.5rem;
  margin-top: 0.25rem;
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  accent-color: var(--primary);
  border-radius: 0.25rem;
  cursor: default;
}

.markdown-content li:has(input[type="checkbox"]) {
  list-style: none;
  display: flex;
  align-items: flex-start;
  margin-left: -2rem;
  padding-left: 2rem;
}

.markdown-content li:has(input[type="checkbox"]) p {
  margin: 0;
}

/* Blockquotes - Enhanced styling for better visibility */
.markdown-content blockquote {
  border-left: 0.25rem solid color-mix(in oklch, var(--primary) 70%, transparent);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  color: color-mix(in oklch, var(--foreground) 90%, transparent);
  background-color: color-mix(in oklch, var(--muted) 30%, transparent);
  border-radius: 0.375rem;
  position: relative;
}

.markdown-content blockquote::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0.25rem;
  background: linear-gradient(to bottom, var(--primary), color-mix(in oklch, var(--primary) 50%, transparent));
  border-radius: 0.125rem;
}

.markdown-content blockquote > :first-child {
  margin-top: 0;
}

.markdown-content blockquote > :last-child {
  margin-bottom: 0;
}

.markdown-content blockquote p {
  color: inherit;
  font-size: inherit;
  line-height: 1.6;
  margin: 0.75rem 0;
}

/* Tables - Enhanced styling with better contrast */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px color-mix(in oklch, var(--border) 30%, transparent);
}

.markdown-content thead {
  background-color: var(--muted);
  border-bottom: 2px solid var(--border);
}

.markdown-content th,
.markdown-content td {
  padding: 0.875rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
  vertical-align: top;
}

.markdown-content th {
  font-weight: 650;
  color: var(--foreground);
  font-size: calc(var(--body-font-size) * 0.9);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.markdown-content td {
  color: var(--foreground);
}

.markdown-content tbody tr {
  transition: background-color 0.15s ease;
}

.markdown-content tbody tr:hover {
  background-color: var(--accent);
}

.markdown-content tbody tr:nth-child(even) {
  background-color: color-mix(in oklch, var(--muted) 40%, transparent);
}

.markdown-content tbody tr:nth-child(even):hover {
  background-color: var(--accent);
}

.markdown-content tbody tr:last-child td {
  border-bottom: none;
}

/* Responsive table behavior */
@media (max-width: 640px) {
  .markdown-content table {
    font-size: calc(var(--body-font-size) * 0.78);
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .markdown-content th,
  .markdown-content td {
    padding: 0.5rem;
  }
}

/* Code styling - Enhanced contrast and readability */
.markdown-content pre:not(.code-block-wrapper pre) {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--muted);
  border: 1px solid var(--border);
  padding: 1.25rem;
  position: relative;
}

.markdown-content code:not(.component-inline-code) {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: var(--font-size-sm);
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Menlo", "Consolas", monospace;
  font-weight: var(--font-medium);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-normal);
  border: 1px solid var(--border);
  word-break: break-word;
  box-shadow: 0 1px 2px color-mix(in oklch, var(--border) 20%, transparent);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Perfect Code Block Styling */
.markdown-content pre code {
  background: transparent;
  border: none;
  padding: 0;
  border-radius: 0;
  font-size: var(--font-size-sm);
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Menlo", "Consolas", monospace;
  font-weight: var(--font-normal);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  word-break: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Perfect Pre Block Styling */
.markdown-content pre {
  overflow-x: auto;
  white-space: pre;
  line-height: var(--leading-relaxed);
  font-size: var(--font-size-sm);
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Menlo", "Consolas", monospace;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.markdown-content pre code {
  white-space: pre;
  word-wrap: normal;
  overflow-wrap: normal;
  display: block;
}

/* Perfect Heading Hierarchy - Apple HIG Inspired */
.markdown-content h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  border-bottom: 2px solid hsl(var(--border));
  padding-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.markdown-content h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-tight);
  margin-top: 2rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.markdown-content h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-normal);
  margin-top: 1.75rem;
  margin-bottom: 1rem;
  color: hsl(var(--foreground));
}

.markdown-content h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.markdown-content h5 {
  font-size: var(--font-size-base);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.markdown-content h6 {
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-wider);
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
  text-transform: uppercase;
}
.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child,
.markdown-content h4:first-child,
.markdown-content h5:first-child,
.markdown-content h6:first-child {
  margin-top: 0;
}
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  scroll-margin-top: 1rem;
}

/* Links - Enhanced styling with better visibility */
.markdown-content a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-decoration-color: hsl(var(--primary) / 0.6);
  text-underline-offset: 0.2rem;
  text-decoration-thickness: 0.125rem;
  word-break: break-word;
  transition: all 0.2s ease;
  font-weight: 500;
  border-radius: 0.125rem;
  padding: 0.125rem 0.25rem;
  margin: -0.125rem -0.25rem;
}

.markdown-content a:hover {
  color: hsl(var(--primary));
  text-decoration-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.1);
  text-decoration-thickness: 0.15rem;
}

.markdown-content a:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 0.25rem;
  background-color: hsl(var(--primary) / 0.1);
}

/* External link indicators - improved positioning */
.markdown-content a[href^="http"]:not([href*="localhost"]):not([href*="127.0.0.1"])::after {
  content: "↗";
  font-size: 0.75em;
  margin-left: 0.125rem;
  color: hsl(var(--muted-foreground));
  display: inline;
  vertical-align: super;
  line-height: 0;
}

/* Definition lists */
.markdown-content dl {
  margin: 1rem 0;
}

.markdown-content dt {
  font-weight: 600;
  margin-bottom: 0.25rem;
  margin-top: 1rem;
}

.markdown-content dt:first-child {
  margin-top: 0;
}

.markdown-content dd {
  margin-left: 1rem;
  margin-bottom: 1rem;
  color: hsl(var(--muted-foreground));
}

/* Details and summary */
.markdown-content details {
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.markdown-content details[open] > summary {
  margin-bottom: 0.75rem;
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 0.5rem;
}

.markdown-content summary {
  font-weight: 600;
  cursor: pointer;
  color: hsl(var(--foreground));
  transition: color 0.2s ease;
  user-select: none;
}

.markdown-content summary:hover {
  color: hsl(var(--primary));
}

/* Horizontal rules */
.markdown-content hr {
  border: none;
  height: 0.25rem;
  background-color: hsl(var(--border));
  margin: 1.5rem 0;
  border-radius: 0.125rem;
}

/* Mark, kbd, abbr - Enhanced styling with better theme support */
.markdown-content mark {
  /* Use theme-aware primary color for highlight to ensure visibility across all themes */
  background-color: hsl(var(--primary) / 0.25);
  color: hsl(var(--primary-foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
}

.dark .markdown-content mark {
  background-color: hsl(var(--primary) / 0.4);
  color: hsl(var(--primary-foreground));
}

.markdown-content kbd {
  background-color: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-bottom: 2px solid hsl(var(--border));
  border-radius: 0.25rem;
  padding: 0.125rem 0.375rem;
  font-size: calc(var(--body-font-size) * 0.67);
  font-family: "JetBrains Mono", "Fira Code", ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-weight: 600;
  box-shadow: 0 1px 2px hsl(var(--border) / 0.3);
  display: inline-block;
  line-height: 1;
}

.markdown-content abbr {
  border-bottom: 1px dotted hsl(var(--muted-foreground));
  cursor: help;
  text-decoration: none;
}

.markdown-content abbr:hover {
  border-bottom-style: solid;
  color: hsl(var(--primary));
}

/* Sup and sub - improved positioning */
.markdown-content sup,
.markdown-content sub {
  font-size: calc(var(--body-font-size) * 0.67);
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.markdown-content sup {
  top: -0.5em;
}

.markdown-content sub {
  bottom: -0.25em;
}

/* Perfect Paragraph Styling */
.markdown-content p {
  margin: 1rem 0;
  font-size: var(--font-size-base);
  line-height: var(--leading-relaxed);
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-normal);
  color: hsl(var(--foreground));
}

.markdown-content p:first-child {
  margin-top: 0;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

/* Perfect Text Formatting */
.markdown-content strong {
  font-weight: var(--font-semibold);
  color: hsl(var(--foreground));
}

.markdown-content em {
  font-style: italic;
  color: hsl(var(--foreground));
}

@layer utilities {
  /* Gradient animation for fancy text backgrounds */
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 6s ease-in-out infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Organic blob movement for background decoration */
  .animate-blob {
    animation: blob 20s infinite;
  }

  @keyframes blob {
    0%, 100% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
  }

  /* Extra glow animation for orbs/borders */
  .animate-orb {
    animation: orbGlow 6s ease-in-out infinite;
  }

  @keyframes orbGlow {
    0% {
      filter: hue-rotate(0deg);
    }
    50% {
      filter: hue-rotate(180deg);
    }
    100% {
      filter: hue-rotate(360deg);
    }
  }
}

@layer components {
  /* Perfect Typography Utilities - Apple HIG Inspired */

  /* Display Typography - For heroes, marketing content */
  .text-display-xl {
    font-size: var(--font-size-8xl);
    line-height: var(--leading-none);
    font-weight: var(--font-black);
    letter-spacing: var(--tracking-tighter);
  }

  .text-display-lg {
    font-size: var(--font-size-6xl);
    line-height: var(--leading-tight);
    font-weight: var(--font-extrabold);
    letter-spacing: var(--tracking-tight);
  }

  .text-display-md {
    font-size: var(--font-size-5xl);
    line-height: var(--leading-tight);
    font-weight: var(--font-bold);
    letter-spacing: var(--tracking-tight);
  }

  .text-display-sm {
    font-size: var(--font-size-4xl);
    line-height: var(--leading-snug);
    font-weight: var(--font-bold);
    letter-spacing: var(--tracking-normal);
  }

  /* Headline Typography - For section headers */
  .text-headline-xl {
    font-size: var(--font-size-3xl);
    line-height: var(--leading-snug);
    font-weight: var(--font-bold);
    letter-spacing: var(--tracking-normal);
  }

  .text-headline-lg {
    font-size: var(--font-size-2xl);
    line-height: var(--leading-snug);
    font-weight: var(--font-semibold);
    letter-spacing: var(--tracking-normal);
  }

  .text-headline-md {
    font-size: var(--font-size-xl);
    line-height: var(--leading-normal);
    font-weight: var(--font-semibold);
    letter-spacing: var(--tracking-normal);
  }

  .text-headline-sm {
    font-size: var(--font-size-lg);
    line-height: var(--leading-normal);
    font-weight: var(--font-medium);
    letter-spacing: var(--tracking-normal);
  }

  /* Body Typography - For content */
  .text-body-xl {
    font-size: var(--font-size-lg);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  .text-body-lg {
    font-size: var(--font-size-base);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  .text-body-md {
    font-size: var(--font-size-sm);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  .text-body-sm {
    font-size: var(--font-size-xs);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-wide);
  }

  /* Label Typography - For UI labels, metadata */
  .text-label-xl {
    font-size: var(--font-size-base);
    line-height: var(--leading-snug);
    font-weight: var(--font-medium);
    letter-spacing: var(--tracking-normal);
  }

  .text-label-lg {
    font-size: var(--font-size-sm);
    line-height: var(--leading-snug);
    font-weight: var(--font-medium);
    letter-spacing: var(--tracking-normal);
  }

  .text-label-md {
    font-size: var(--font-size-xs);
    line-height: var(--leading-snug);
    font-weight: var(--font-medium);
    letter-spacing: var(--tracking-wide);
  }

  .text-label-sm {
    font-size: var(--font-size-xs);
    line-height: var(--leading-tight);
    font-weight: var(--font-medium);
    letter-spacing: var(--tracking-wider);
    text-transform: uppercase;
  }

  /* Caption Typography - For small text, captions */
  .text-caption-lg {
    font-size: var(--font-size-sm);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  .text-caption-md {
    font-size: var(--font-size-xs);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-wide);
  }

  .text-caption-sm {
    font-size: 0.6875rem; /* 11px */
    line-height: var(--leading-tight);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-wider);
  }

  /* Code Typography - For monospace content */
  .text-code-lg {
    font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Menlo", "Consolas", monospace;
    font-size: var(--font-size-base);
    line-height: var(--leading-relaxed);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  .text-code-md {
    font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Menlo", "Consolas", monospace;
    font-size: var(--font-size-sm);
    line-height: var(--leading-normal);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  .text-code-sm {
    font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", "Menlo", "Consolas", monospace;
    font-size: var(--font-size-xs);
    line-height: var(--leading-tight);
    font-weight: var(--font-normal);
    letter-spacing: var(--tracking-normal);
  }

  /* Vision-inspired liquid glass surface that adapts to theme */
  .liquid-glass {
    /* Layout */
    @apply relative overflow-hidden rounded-3xl backdrop-blur-2xl shadow-2xl border border-white/30 dark:border-white/10;

    /* Light theme tint */
    background: rgba(255, 255, 255, 0.45);

    /* Enhanced glass shadow */
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.05),
      0 8px 32px rgba(0, 0, 0, 0.12);
    /* Improve readability of foreground content */
    -webkit-backdrop-filter: blur(18px) saturate(160%) contrast(110%);
    backdrop-filter: blur(18px) saturate(160%) contrast(110%);
  }

  /* Dark theme tint override */
  .dark .liquid-glass {
    background: rgba(255, 255, 255, 0.08);
  }

  /* Subtle inner glow that mimics light refraction on glass */
  .liquid-glass::before {
    content: "";
    position: absolute;
    inset: 0;
    pointer-events: none;
    background: radial-gradient(ellipse at 50% 0%, rgba(255,255,255,0.6) 0%, transparent 70%);
    mix-blend-mode: overlay;
  }

  /* Liquid-like glow behind the glass */
  .liquid-glass::after {
    content: "";
    position: absolute;
    z-index: -1;
    inset: -40%; /* extend beyond bounds to accent blur */
    background: radial-gradient(circle at 50% 50%, hsl(var(--primary) / 0.35) 0%, transparent 70%);
    filter: blur(40px);
    opacity: 0.7;
    transition: opacity 0.3s ease;
  }
  .dark .liquid-glass::after {
    opacity: 0.5;
  }
}

/* UI Themes */
.ui-rounded {
  --radius: 1.5rem;
}

.ui-square {
  --radius: 0.25rem;
}

.ui-glass {
  --card: hsla(var(--card) / 0.65);
  --popover: hsla(var(--popover) / 0.65);
  --background: hsla(var(--background) / 0.6);
  backdrop-filter: blur(12px);
}
@layer components {
  /* --- Improved Glassmorphism Theme --- */
  .ui-glass {
    --glass-backdrop-filter: blur(20px) saturate(150%);
    --glass-border-color: hsl(var(--border) / 0.25);
    --glass-shadow: 0 10px 40px hsl(var(--primary) / 0.15);

    /* Adjusted backgrounds for better visibility */
    --glass-bg-card: hsl(var(--card) / 0.6);
    --glass-bg-popover: hsl(var(--popover) / 0.7);

    --glass-tab-bar-bg: hsl(var(--muted) / 0.15);
    --glass-tab-bar-border: hsl(var(--border) / 0.25);
    --glass-tab-active-bg: hsl(var(--primary) / 0.15);
    --glass-tab-active-border: hsl(var(--primary));
  }

  .dark.ui-glass {
    --glass-backdrop-filter: blur(24px) saturate(160%);
    --glass-border-color: hsl(var(--border) / 0.15);
    --glass-shadow: 0 10px 40px hsl(var(--primary) / 0.1);

    --glass-bg-card: hsl(var(--card) / 0.15);
    --glass-bg-popover: hsl(var(--popover) / 0.2);

    --glass-tab-bar-bg: hsl(var(--muted) / 0.25);
  }

  /* Add subtle hover effect for glass components */
  .ui-glass .bg-card:hover,
  .ui-glass .themed-card:hover,
  .ui-glass .bg-popover:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
    box-shadow: 0 12px 48px hsl(var(--primary) / 0.2) !important;
  }

  /*
    Now, we apply these variables to the standard .bg-card and .bg-popover classes.
    This means any component using these utilities will automatically become glass
    when the .ui-glass theme is enabled. No more manual class changes!
  */
  .ui-glass .bg-card,
  .ui-glass .themed-card,
  .ui-glass .bg-popover {
    background-color: var(--glass-bg-popover) !important;
    border-color: var(--glass-border-color) !important;
    backdrop-filter: var(--glass-backdrop-filter);
    -webkit-backdrop-filter: var(--glass-backdrop-filter);
    box-shadow: var(--glass-shadow) !important;
  }
  .ui-glass .bg-card,
  .ui-glass .themed-card {
    background-color: var(--glass-bg-card) !important;
  }

  /* --- Tab styling within glass components --- */
  .ui-glass .glass-tab-bar {
    background-color: var(--glass-tab-bar-bg);
    border-bottom-color: var(--glass-tab-bar-border);
  }
  .ui-glass .glass-tab-active {
    background-color: var(--glass-tab-active-bg);
    border-bottom-color: var(--glass-tab-active-border);
  }
}

/* UI Themes */
.ui-rounded {
  --radius: 1.5rem;
}

.ui-square {
  --radius: 0.25rem;
}

/* This is now just a marker class. The magic happens in the @layer above. */
.ui-glass {}

:root {
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  --sidebar: oklch(0.0500 0 0);  /* Pure black Apple sidebar */
  --sidebar-foreground: oklch(0.8000 0 0);
  --sidebar-primary: oklch(0.9500 0 0);
  --sidebar-primary-foreground: oklch(0.0500 0 0);
  --sidebar-accent: oklch(0.1500 0 0);
  --sidebar-accent-foreground: oklch(0.9500 0 0);
  --sidebar-border: oklch(0.1500 0 0);
  --sidebar-ring: oklch(0.7000 0 0);
  --background: oklch(0.1000 0 0);  /* Pure black Apple background */
  --foreground: oklch(0.9500 0 0);  /* Clean white text */
  --card: oklch(0.1200 0 0);  /* Very dark gray cards */
  --card-foreground: oklch(0.9500 0 0);
  --popover: oklch(0.1200 0 0);
  --popover-foreground: oklch(0.9500 0 0);
  --primary: oklch(0.9500 0 0);  /* Clean white primary */
  --primary-foreground: oklch(0.1000 0 0);
  --secondary: oklch(0.2000 0 0);  /* Clean dark gray */
  --secondary-foreground: oklch(0.9500 0 0);
  --muted: oklch(0.2000 0 0);  /* Clean dark gray */
  --muted-foreground: oklch(0.7000 0 0);  /* Clean light gray */
  --accent: oklch(0.2200 0 0);  /* Clean accent */
  --accent-foreground: oklch(0.9500 0 0);
  --destructive: oklch(0.5000 0.2000 30);  /* Apple red */
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.2500 0 0);  /* Clean dark border */
  --input: oklch(0.2000 0 0);  /* Clean dark input */
  --ring: oklch(0.7000 0 0);
  --chart-1: oklch(0.6231 0.1880 259.8145);
  --chart-2: oklch(0.6268 0.2325 303.9004);
  --chart-3: oklch(0.7686 0.1647 70.0804);
  --chart-4: oklch(0.6959 0.1491 162.4796);
  --chart-5: oklch(0.6368 0.2078 25.3313);
  --radius: 0.75rem;
  --font-sans: Inter;
  --font-serif: Georgia;
  --font-mono: JetBrains Mono;
  --shadow-color: #000000;
  --shadow-opacity: 0.1;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0rem;
  --spacing: 0.25rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: Inter;
  --font-mono: JetBrains Mono;
  --font-serif: Georgia;
  --radius: 0.75rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-foreground: var(--foreground);
  --color-background: var(--background);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.animate-gradient {
  background-size: 300%;
  -webkit-animation: animatedgradient 6s ease infinite alternate;
  -moz-animation: animatedgradient 6s ease infinite alternate;
  animation: animatedgradient 6s ease infinite alternate;
}

@keyframes animatedgradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-fade-in {
  animation: fadeIn 1s ease-in-out;
}
.animation-delay-300 {
  animation-delay: 300ms;
}
.animation-delay-600 {
  animation-delay: 600ms;
}
.animation-delay-900 {
  animation-delay: 900ms;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-up {
  opacity: 0;
  animation: fadeUp 0.8s forwards;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-left {
  opacity: 0;
  animation: fadeLeft 0.8s forwards;
}

@keyframes fadeLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-right {
  opacity: 0;
  animation: fadeRight 0.8s forwards;
}

@keyframes fadeRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-blob {
  animation: blob 15s infinite;
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.2);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.animate-bounce-slight {
  animation: bounce-slight 2s infinite;
}

@keyframes bounce-slight {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes aurora-border {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(50%);
  }
}

.animate-aurora-border {
  animation: aurora-border 6s infinite linear;
  will-change: transform;
}

/* Floating animation for elegant orbs */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(1deg);
  }
  50% {
    transform: translateY(-10px) rotate(-1deg);
  }
  75% {
    transform: translateY(-15px) rotate(0.5deg);
  }
}

.animate-float {
  animation: float 20s ease-in-out infinite;
  will-change: transform;
}

/* Slow spin animation for rings */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 30s linear infinite;
  will-change: transform;
}

/* Additional animation delay classes */
.animation-delay-1000 {
  animation-delay: 1000ms;
}

.animation-delay-1500 {
  animation-delay: 1500ms;
}

.animation-delay-2000 {
  animation-delay: 2000ms;
}

.animation-delay-3000 {
  animation-delay: 3000ms;
}

.animation-delay-3500 {
  animation-delay: 3500ms;
}

.animation-delay-4000 {
  animation-delay: 4000ms;
}

/* Code Block Wrapper Styling - Prevents CSS conflicts with CodeBlock components */
.code-block-wrapper {
  margin: 1rem 0;
}

.code-block-wrapper pre {
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
}

/* Enhanced typing indicator animations */
@keyframes typing-wave {
  0%, 100% { 
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% { 
    transform: translateY(-2px) scale(1.1);
    opacity: 1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

@keyframes typing-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.7;
  }
  50% { 
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes typing-fade {
  0%, 100% { 
    opacity: 0.3;
  }
  50% { 
    opacity: 1;
  }
}

@keyframes typing-bounce-smooth {
  0%, 100% { 
    transform: translateY(0px);
    opacity: 0.8;
  }
  50% { 
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* Perfect Typography Settings Applied */
/* All typography now uses the perfect system defined above */

/* --- Dark-mode markdown readability tweaks --- */
@layer base {
  /* Enhanced text contrast for better readability */
  .dark .markdown-content {
    color: var(--foreground);
  }

  /* Ensure all text elements are bright enough */
  .dark .markdown-content p,
  .dark .markdown-content li,
  .dark .markdown-content span,
  .dark .markdown-content div {
    color: var(--foreground);
  }

  /* Improved headings visibility in dark mode */
  .dark .markdown-content h1,
  .dark .markdown-content h2,
  .dark .markdown-content h3,
  .dark .markdown-content h4,
  .dark .markdown-content h5 {
    color: var(--foreground) !important;
    border-bottom-color: var(--border);
  }

  .dark .markdown-content h6 {
    color: var(--muted-foreground) !important;
  }

  /* Improve dark link contrast and visibility */
  .dark .markdown-content a {
    color: var(--primary);
    text-decoration-color: color-mix(in oklch, var(--primary) 70%, transparent);
    font-weight: 500;
  }
  .dark .markdown-content a:hover {
    color: var(--primary);
    text-decoration-color: var(--primary);
  }

  /* Enhanced blockquotes with better contrast */
  .dark .markdown-content blockquote {
    border-left: 0.25rem solid color-mix(in oklch, var(--primary) 60%, transparent);
    background: color-mix(in oklch, var(--muted) 30%, transparent);
    color: var(--foreground);
    border-radius: 0.375rem;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
  }

  .dark .markdown-content blockquote p {
    color: inherit;
  }

  /* Better table visibility in dark mode */
  .dark .markdown-content table {
    background-color: var(--card);
    border-color: var(--border);
  }

  .dark .markdown-content thead {
    background-color: var(--muted);
  }

  .dark .markdown-content th {
    color: var(--foreground);
    border-bottom-color: var(--border);
  }

  .dark .markdown-content td {
    color: var(--foreground);
    border-bottom-color: var(--border);
  }

  .dark .markdown-content tbody tr:hover {
    background-color: var(--accent);
  }
  .dark .markdown-content tbody tr:nth-child(even) {
    background-color: color-mix(in oklch, var(--muted) 30%, transparent);
  }
  .dark .markdown-content tbody tr:nth-child(even):hover {
    background-color: var(--accent);
  }

  /* Enhanced code styling for better contrast */
  .dark .markdown-content pre:not(.code-block-wrapper pre) {
    background-color: var(--muted);
    border-color: var(--border);
    color: var(--foreground);
  }

  .dark .markdown-content code:not(.component-inline-code) {
    background-color: var(--muted);
    border-color: var(--border);
    color: var(--foreground);
    font-weight: 500;
  }

  /* Improved inline elements */
  .dark .markdown-content strong {
    color: var(--foreground) !important;
    font-weight: 700;
  }

  .dark .markdown-content em {
    color: var(--foreground);
  }

  /* Better mark highlighting in dark mode */
  .dark .markdown-content mark {
    background-color: color-mix(in oklch, var(--primary) 30%, transparent);
    color: var(--primary-foreground);
    border-radius: 0.25rem;
    padding: 0.125rem 0.25rem;
  }

  /* Enhanced kbd styling */
  .dark .markdown-content kbd {
    background-color: var(--muted);
    border-color: var(--border);
    color: var(--foreground);
    box-shadow: 0 2px 4px color-mix(in oklch, var(--border) 50%, transparent);
  }

  /* Better horizontal rule visibility */
  .dark .markdown-content hr {
    background-color: var(--border);
    opacity: 0.8;
  }

  /* Improved list markers */
  .dark .markdown-content li::marker {
    color: var(--primary);
  }

  /* Enhanced definition lists */
  .dark .markdown-content dt {
    color: var(--foreground);
  }

  .dark .markdown-content dd {
    color: var(--muted-foreground);
  }

  /* Better details/summary styling */
  .dark .markdown-content details {
    background-color: color-mix(in oklch, var(--muted) 20%, transparent);
    border-color: var(--border);
  }

  .dark .markdown-content summary {
    color: var(--foreground);
  }

  .dark .markdown-content summary:hover {
    color: var(--primary);
  }

  /* Force visibility for any text that might be inheriting bad colors */
  .dark .markdown-content * {
    color: inherit;
  }

  /* Ensure specific text elements have proper contrast */
  .dark .markdown-content h1 *,
  .dark .markdown-content h2 *,
  .dark .markdown-content h3 *,
  .dark .markdown-content h4 *,
  .dark .markdown-content h5 *,
  .dark .markdown-content h6 * {
    color: inherit !important;
  }

  /* Enhanced selection contrast */
  .dark ::selection { 
    background: color-mix(in oklch, var(--primary) 25%, transparent); 
    color: var(--background);
  }
  .dark ::-moz-selection { 
    background: color-mix(in oklch, var(--primary) 25%, transparent); 
    color: var(--background);
  }

  /* Code selection with better contrast */
  .dark .markdown-content code ::selection,
  .dark .markdown-content pre ::selection {
    background: color-mix(in oklch, var(--primary) 35%, transparent);
    color: var(--background);
  }

  .dark .markdown-content code ::-moz-selection,
  .dark .markdown-content pre ::-moz-selection {
    background: color-mix(in oklch, var(--primary) 35%, transparent);
    color: var(--background);
  }
}

@layer components {
  /* Ensure glass theme remains legible in dark */
  .dark.ui-glass .bg-card,
  .dark.ui-glass .themed-card,
  .dark.ui-glass .bg-popover {
    box-shadow: 0 10px 40px color-mix(in oklch, var(--primary) 14%, transparent) !important;
    border-color: color-mix(in oklch, var(--border) 90%, transparent) !important;
  }
}