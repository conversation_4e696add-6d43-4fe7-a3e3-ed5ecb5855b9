import { useEffect, useRef, useCallback, memo, useMemo } from "react";
import { Id } from "../../convex/_generated/dataModel";
import { MessageBubble } from "./MessageBubble";
import { MessageCircle, Lightbulb, Code, Plane, Sparkles, ArrowRight, Briefcase, BarChart2, GraduationCap, Film, PenTool, Heart, GitBranch, Target, Globe, Calculator, Image, Bug, FileText, Music, Dumbbell, Coffee, Palette, BookOpen, Database, Mail, TrendingUp, MapPin, Leaf, HeartPulse, UtensilsCrossed, Camera, Megaphone, PencilLine } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { LoadingDots } from "@/components/ui/loading-dots";
import { useMutation, useAction, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { getModelInfo } from "@/lib/models";
import { getMinimalError } from "@/lib/errorUtils";
import { toast } from "sonner";

interface ChatAreaProps {
  messages: any[];
  isGenerating: boolean;
  conversationId: Id<"conversations"> | null;
  currentBranchId?: string;
  shouldScrollToBottom?: boolean;
  onSwitchBranch?: (branchId: string) => void;
  onBranchChanged?: (branchId: string) => void;
  onScrolled?: () => void;
  bottomPadding?: number;
  onSendMessage?: (content: string) => void;
}

export const ChatArea = memo(function ChatArea({ messages, isGenerating, conversationId, currentBranchId, shouldScrollToBottom, onSwitchBranch, onBranchChanged, onScrolled, bottomPadding, onSendMessage }: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const lastScrollPosition = useRef<number>(0);

  // Find the index of the last message with canvas data
  const lastCanvasMessageIndex = (() => {
    if (!Array.isArray(messages)) return -1;
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i] && messages[i].canvasData) {
        return i;
      }
    }
    return -1;
  })();

  // Mark older canvas messages as outdated
  const processedMessages = (Array.isArray(messages) ? messages.map((message, index) => {
    if (message.canvasData && index !== lastCanvasMessageIndex) {
      return { ...message, isOutdatedCanvas: true };
    }
    return message;
  }) : []);

  // Mutations for branching functionality
  const copyMessage = useMutation(api.messages.copyMessage);
  const editMessage = useMutation(api.messages.editMessage);
  const retryMessage = useMutation(api.messages.retryMessage);
  const branchOffConversation = useMutation(api.branches.branchOffConversation);
  const generateStreamingResponse = useAction(api.ai.generateStreamingResponse);
  
  // Query for preferences
  const preferences = useQuery(api.preferences.get);
  // Fetch usage info to determine subscription plan for research retries
  const usage = useQuery(api.usage.get);

  // Memoized scroll function to prevent recreation
  const scrollToBottom = useCallback((force = false) => {
    // Clear any existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Use timeout to batch scroll operations and prevent excessive calls
    scrollTimeoutRef.current = setTimeout(() => {
      if (messagesEndRef.current) {
        if (force) {
          // Immediate scroll for user actions
          messagesEndRef.current.scrollIntoView({ behavior: "auto" });
        } else {
          // Smooth scroll for other cases
          messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
        }
      }
    }, force ? 0 : 50); // Longer delay for non-force scrolls to batch better
  }, []);

  // Handle scroll to bottom trigger from parent - with proper cleanup
  useEffect(() => {
    if (shouldScrollToBottom) {
      scrollToBottom(true); // Force immediate scroll when user sends message
      // Use a timeout to ensure onScrolled is called after scroll completes
      const timeoutId = setTimeout(() => {
        onScrolled?.(); // Notify parent that we've scrolled
      }, 50); // Give time for scroll to complete
      
      return () => clearTimeout(timeoutId);
    }
  }, [shouldScrollToBottom, onScrolled, scrollToBottom]);

  // Only auto-scroll during generation if we're near the bottom - OPTIMIZED
  useEffect(() => {
    if (isGenerating && !shouldScrollToBottom) {
      // Debounce this check to prevent excessive DOM queries
      const timeoutId = setTimeout(() => {
        const container = messagesEndRef.current?.parentElement?.parentElement;
        if (container) {
          const { scrollTop, scrollHeight, clientHeight } = container;
          const isNearBottom = scrollHeight - scrollTop - clientHeight < 200;
          
          // Only scroll if near bottom AND scroll position changed significantly
          if (isNearBottom && Math.abs(scrollTop - lastScrollPosition.current) > 50) {
            lastScrollPosition.current = scrollTop;
            scrollToBottom(); // Smooth scroll if near bottom
          }
        }
      }, 200); // Increase debounce time to reduce frequency
      
      return () => clearTimeout(timeoutId);
    }
  }, [messages?.length, isGenerating, shouldScrollToBottom]); // Remove scrollToBottom from deps to prevent recreation

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Action handlers
  const handleCopyMessage = useCallback(async (messageId: Id<"messages">) => {
    try {
      const message = messages.find(m => m._id === messageId);
      if (message) {
        await navigator.clipboard.writeText(message.content);
        toast.success("Copied to clipboard", {
          description: "Message content has been copied successfully",
        });
      }
    } catch (error) {
      console.error("Failed to copy message:", error);
      toast.error("Copy Failed", {
        description: "Unable to copy message to clipboard. Please try again.",
      });
    }
  }, [messages]);

  const handleEditMessage = useCallback(async (messageId: Id<"messages">, newContent: string) => {
    try {
      if (!conversationId) return;
      
      const result = await editMessage({
        messageId,
        newContent,
      });
      
      // If edit was successful, notify parent of branch change and automatically regenerate AI response
      if (result?.branchId) {
        // Notify parent component of the branch change
        onBranchChanged?.(result.branchId);
        
        // Get the conversation messages up to the edit point to regenerate
        const messageList = Array.isArray(messages) ? messages : [];
        const targetMessage = messageList.find(m => m._id === messageId);
        
        // Build message history including the new edited message
        const messageHistory = messageList
          .filter(msg => targetMessage && msg._creationTime < targetMessage._creationTime)
          .map((msg: any) => ({
            role: msg.role as "user" | "assistant" | "system",
            content: msg.content,
          }));
        
        // Add the edited message to the history
        messageHistory.push({
          role: "user" as const,
          content: newContent,
        });

        // Get user preferences for AI generation
        const provider = preferences?.aiProvider ?? "google";
        const model = preferences?.model ?? "gemini-2.5-flash";
        const temperature = preferences?.temperature ?? 1;
        const toolsFromPrefs = (preferences?.enabledTools as any[]) || [];
        
        // Check if the selected model supports tools
        const modelInfo = getModelInfo(model);
        const enabledTools = modelInfo.supportsTools ? toolsFromPrefs : [];

        // Generate new response
        await generateStreamingResponse({
          conversationId,
          branchId: result.branchId,
          messages: messageHistory,
          provider: provider as any,
          model,
          temperature,
          enabledTools,
        });
      }
    } catch (error) {
      console.error("Failed to edit message:", error);
      
      // Show minimal error message
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      const errorSummary = getMinimalError(errorMessage);
      const toastMessage = errorSummary.message;
      const toastDescription = errorSummary.action || "Try again";
      
      toast.error(toastMessage, {
        description: toastDescription,
        action: {
          label: "Open Settings",
          onClick: () => {
            window.history.pushState({}, '', '/settings');
            window.dispatchEvent(new PopStateEvent('popstate'));
          },
        },
      });
    }
  }, [conversationId, editMessage, onBranchChanged, messages, preferences, generateStreamingResponse]);

  const handleRetryMessage = useCallback(async (messageId: Id<"messages">) => {
    try {
      if (!conversationId) return;
      
      const result = await retryMessage({
        messageId,
      });
      
      // If retry was successful and needs regeneration, trigger AI
      if (result?.needsRegeneration && result?.branchId) {
        // Notify parent component of the branch change
        onBranchChanged?.(result.branchId);
        
        // Get the conversation messages up to the retry point to regenerate
        const messageList = Array.isArray(messages) ? messages : [];
        const targetMessage = messageList.find(m => m._id === messageId);
        const messageHistory = messageList
          .filter(msg => targetMessage && msg._creationTime < targetMessage._creationTime)
          .map((msg: any) => ({
            role: msg.role as "user" | "assistant" | "system",
            content: msg.content,
          }));

        let provider = preferences?.aiProvider || "google";
        let model = preferences?.model || "gemini-2.5-flash";
        let enabledTools: string[] = [];
        const temperature = preferences?.temperature || 1;

        // If the original message involved the research tool, use deep research model based on plan
        if (targetMessage?.toolCalls?.some((tc: any) => tc.name === "research")) {
          provider = "openai";
          const plan = usage?.plan ?? "free";
          model = plan === "ultra" || plan === "max" ? "o4-mini-deep-research" : "o4-mini-deep-research";
          enabledTools = []; // research model handles internally
        } else {
          const toolsFromPrefs = (preferences?.enabledTools as any[]) || [];
          const modelInfo = getModelInfo(model);
          enabledTools = modelInfo.supportsTools ? toolsFromPrefs : [];
        }

        // Generate new response
        await generateStreamingResponse({
          conversationId,
          branchId: result.branchId,
          messages: messageHistory,
          provider: provider as any,
          model,
          temperature,
          enabledTools,
        });
      }
    } catch (error) {
      console.error("Failed to retry message:", error);
      
      // Show minimal error message
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      const errorSummary = getMinimalError(errorMessage);
      const toastMessage = errorSummary.message;
      const toastDescription = errorSummary.action || "Try again";
      
      toast.error(toastMessage, {
        description: toastDescription,
      });
    }
  }, [conversationId, retryMessage, onBranchChanged, messages, preferences, generateStreamingResponse, usage]);

  const handleBranchOff = useCallback(async (
    messageId: Id<"messages">,
    provider?: string,
    model?: string,
  ) => {
    try {
      if (!conversationId) return;

      // Find the message object to determine its role
      const branchPointMessage = processedMessages.find((m) => m._id === messageId);
      if (!branchPointMessage) return;

      // Auto-generate a title for the new conversation
      const originalContent = branchPointMessage.content as string;
      const truncatedContent = originalContent.length > 50
        ? originalContent.substring(0, 50).trim() + "..."
        : originalContent;
      const title = `Branch off: ${truncatedContent}`;

      // 1. Create the branched conversation
      const newConversationId = await branchOffConversation({
        conversationId,
        branchPoint: messageId,
        title: title.trim(),
      });

      // 2. If the branch point is a user message, immediately generate an AI response
      if (branchPointMessage.role === "user") {
        // Build message history up to and including the branch point
        const msgIndex = processedMessages.findIndex((m) => m._id === messageId);
        const history = processedMessages
          .slice(0, msgIndex + 1)
          .map((m) => ({ role: m.role, content: m.content }));

        const prefsProvider = preferences?.aiProvider || "google";
        const prefsModel = preferences?.model || "gemini-2.5-flash";
        const providerToUse = provider || prefsProvider;
        const modelToUse = model || prefsModel;
        const temperature = preferences?.temperature ?? 1;
        const enabledTools = (preferences?.enabledTools as any[]) ?? [];

        await generateStreamingResponse({
          conversationId: newConversationId,
          messages: history,
          provider: providerToUse,
          model: modelToUse,
          temperature,
          enabledTools,
        });
      }

      // Show success notification
      toast.success("Branch Created", {
        description: "New conversation branch created successfully.",
      });
    } catch (error) {
      console.error("Failed to branch off conversation:", error);
      
      // Show minimal error message
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      const errorSummary = getMinimalError(errorMessage);
      const toastMessage = errorSummary.message;
      const toastDescription = errorSummary.action || "Try again";
      
      toast.error(toastMessage, {
        description: toastDescription,
        action: {
          label: "Open Settings",
          onClick: () => {
            window.history.pushState({}, '', '/settings');
            window.dispatchEvent(new PopStateEvent('popstate'));
          },
        },
      });
    }
  }, [conversationId, branchOffConversation, processedMessages, preferences, generateStreamingResponse]);

  if (!conversationId) {
    const allExamplePrompts = [
      {
        icon: <Lightbulb size={20} className="text-yellow-400" />,
        title: "Explain a concept",
        description: "Quantum Computing in simple terms",
        prompt: "Explain Quantum Computing in simple terms",
      },
      {
        icon: <Code size={20} className="text-blue-400" />,
        title: "Write code",
        description: "Python script to merge dictionaries",
        prompt: "Write a python script that merges two dictionaries",
      },
      {
        icon: <Plane size={20} className="text-green-400" />,
        title: "Plan a trip",
        description: "A 3-day itinerary for Tokyo",
        prompt: "Plan a 3-day trip to Tokyo, Japan, including famous landmarks and food recommendations.",
      },
      {
        icon: <Sparkles size={20} className="text-purple-400" />,
        title: "Creative writing",
        description: "A story about a robot discovering music",
        prompt: "Write a short story about a friendly robot who discovers music for the first time.",
      },
      {
        icon: <Briefcase size={20} className="text-indigo-400" />,
        title: "Draft an email",
        description: "A follow-up after a job interview",
        prompt: "Draft a professional follow-up email after a job interview for a software engineer position.",
      },
      {
        icon: <BarChart2 size={20} className="text-red-400" />,
        title: "Analyze data",
        description: "Trends in a customer feedback dataset",
        prompt: "Analyze the following dataset of customer feedback and identify the main trends and sentiment.",
      },
      {
        icon: <GraduationCap size={20} className="text-orange-400" />,
        title: "Help me learn",
        description: "The main causes of World War I",
        prompt: "Summarize the main causes of World War I for a high school student.",
      },
      {
        icon: <Film size={20} className="text-pink-400" />,
        title: "Movie suggestions",
        description: "Sci-fi movies similar to Blade Runner",
        prompt: "Can you suggest some sci-fi movies that have a similar atmosphere to Blade Runner?",
      },
      {
        icon: <PenTool size={20} className="text-teal-400" />,
        title: "Summarize text",
        description: "Summarize a long article",
        prompt: "Summarize a long article into three key bullet points.",
      },
      {
        icon: <Heart size={20} className="text-rose-400" />,
        title: "Get recipe ideas",
        description: "A healthy dinner with chicken and rice",
        prompt: "Give me a recipe for a healthy and easy-to-make dinner with chicken breast and brown rice.",
      },
      {
        icon: <GitBranch size={20} className="text-gray-400" />,
        title: "Git command",
        description: "How to revert the last commit",
        prompt: "What is the git command to revert the last commit without deleting the changes?",
      },
      {
        icon: <Target size={20} className="text-cyan-400" />,
        title: "Brainstorm ideas",
        description: "Marketing slogans for a new coffee shop",
        prompt: "Brainstorm 10 catchy marketing slogans for a new, cozy coffee shop that specializes in artisanal pastries.",
      },
      {
        icon: <Globe size={20} className="text-blue-500" />,
        title: "Translate text",
        description: "English to Spanish translation",
        prompt: "Translate the following text from English to Spanish: \"Good morning, how are you?\"",
      },
      {
        icon: <Calculator size={20} className="text-yellow-500" />,
        title: "Solve math",
        description: "Calculate compound interest",
        prompt: "Calculate the compound interest on a $5,000 investment at 5% annual interest compounded monthly for 3 years.",
      },
      {
        icon: <Image size={20} className="text-purple-500" />,
        title: "Generate image",
        description: "Futuristic city at sunset",
        prompt: "Create a detailed prompt for an AI image generator to depict a futuristic city skyline at sunset with flying cars.",
      },
      {
        icon: <Bug size={20} className="text-red-500" />,
        title: "Debug code",
        description: "Fix a Python recursion error",
        prompt: "Fix the following Python code that raises a recursion error and explain the solution: \n```python\n def factorial(n):\n   return n * factorial(n-1)\n```",
      },
      {
        icon: <FileText size={20} className="text-indigo-500" />,
        title: "Write resume summary",
        description: "Professional summary for a software engineer",
        prompt: "Write a concise professional summary for a resume of a software engineer with 5 years of experience in full-stack development and AI.",
      },
      {
        icon: <Music size={20} className="text-pink-500" />,
        title: "Song lyrics",
        description: "Chorus for uplifting pop song",
        prompt: "Write an uplifting and catchy chorus for a pop song about following your dreams.",
      },
      {
        icon: <Dumbbell size={20} className="text-orange-500" />,
        title: "Workout plan",
        description: "30-minute full-body workout",
        prompt: "Create a 30-minute full-body home workout routine that requires no equipment.",
      },
      {
        icon: <Coffee size={20} className="text-amber-500" />,
        title: "Brew coffee",
        description: "Perfect pour-over guide",
        prompt: "Explain step-by-step how to brew the perfect pour-over coffee using a V60 dripper.",
      },
      {
        icon: <Palette size={20} className="text-emerald-500" />,
        title: "Design logo",
        description: "Logo concept for eco startup",
        prompt: "Suggest three unique logo concepts for an eco-friendly startup named \"GreenWave\".",
      },
      {
        icon: <BookOpen size={20} className="text-blue-600" />,
        title: "Summarize book",
        description: "\"Atomic Habits\" key insights",
        prompt: "Summarize the key insights from the book \"Atomic Habits\" by James Clear in bullet points.",
      },
      {
        icon: <Database size={20} className="text-cyan-600" />,
        title: "SQL query",
        description: "Join orders with customers",
        prompt: "Write an SQL query to list all orders with customer names and order totals from tables `orders` and `customers`.",
      },
      {
        icon: <Mail size={20} className="text-violet-500" />,
        title: "Craft email",
        description: "Cold outreach for SaaS product",
        prompt: "Compose a friendly cold outreach email to introduce a SaaS productivity tool to a potential client.",
      },
      {
        icon: <TrendingUp size={20} className="text-green-500" />,
        title: "Market analysis",
        description: "Electric vehicle industry trends",
        prompt: "Provide a brief market analysis of current trends and future projections in the electric vehicle industry.",
      },
      {
        icon: <MapPin size={20} className="text-rose-500" />,
        title: "Find attractions",
        description: "Top landmarks in Paris",
        prompt: "List the top 5 must-see landmarks in Paris and explain why they are popular.",
      },
      {
        icon: <Leaf size={20} className="text-green-600" />,
        title: "Gardening tips",
        description: "Growing tomatoes in pots",
        prompt: "Give detailed advice on how to successfully grow cherry tomatoes in pots on a balcony.",
      },
      {
        icon: <HeartPulse size={20} className="text-red-600" />,
        title: "Health advice",
        description: "Exercises for knee pain",
        prompt: "Recommend low-impact exercises suitable for strengthening knees and reducing pain.",
      },
      {
        icon: <UtensilsCrossed size={20} className="text-orange-600" />,
        title: "Meal prep",
        description: "Weekly vegetarian meal plan",
        prompt: "Create a balanced 5-day vegetarian meal prep plan including breakfast, lunch, and dinner.",
      },
      {
        icon: <Camera size={20} className="text-gray-500" />,
        title: "Photo editing",
        description: "Improve portrait lighting",
        prompt: "Explain how to improve lighting and remove blemishes in a portrait photo using Adobe Lightroom.",
      },
      {
        icon: <Megaphone size={20} className="text-yellow-600" />,
        title: "Social media post",
        description: "Launch announcement caption",
        prompt: "Write an engaging Instagram caption announcing the launch of a new mobile app.",
      },
      {
        icon: <PencilLine size={20} className="text-gray-600" />,
        title: "Outline essay",
        description: "Climate change essay structure",
        prompt: "Create a detailed outline for a 1,500-word essay discussing the effects of climate change on coastal cities.",
      },
    ];

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const examplePrompts = useMemo(() => {
      const shuffled = [...allExamplePrompts].sort(() => 0.5 - Math.random());
      return shuffled.slice(0, 4);
    }, []);

    return (
      <>
        {/* Compact & modern welcome panel */}
        <div className="flex-1 overflow-y-auto p-4 sm:p-6 flex flex-col items-center">
          <div className="text-center max-w-xl mx-auto w-full space-y-3 mt-10 sm:mt-16">
            <h2 className="text-2xl sm:text-3xl font-semibold tracking-tight">
              Welcome to ErzenAI
            </h2>
            <p className="text-sm sm:text-base text-muted-foreground">
              How can I help you today? Ask me anything or pick an example below.
            </p>
          </div>

          <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 w-full max-w-2xl">
            {examplePrompts.map((item, index) => (
              <div
                key={index}
                onClick={() => onSendMessage?.(item.prompt)}
                className="group relative bg-muted/60 hover:bg-background border border-transparent rounded-lg p-4 cursor-pointer transition-shadow shadow-sm hover:shadow-md hover:border-primary/30"
              >
                <div className="flex items-start gap-3">
                  <div className="p-1.5 bg-background rounded-md">
                    {item.icon}
                  </div>
                  <div>
                    <h3 className="font-medium text-sm text-foreground">
                      {item.title}
                    </h3>
                    <p className="text-xs text-muted-foreground mt-0.5">
                      {item.description}
                    </p>
                  </div>
                </div>
                <ArrowRight size={14} className="absolute top-4 right-4 text-muted-foreground group-hover:text-primary transition-colors" />
              </div>
            ))}
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-2 sm:p-6 md:p-8 bg-gradient-to-b from-background/50 to-background" style={{ paddingBottom: (bottomPadding ?? 176) + 32 }}>
      <div className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
        {messages.length === 0 && !isGenerating ? (
          <div className="text-center py-16 sm:py-24">
            <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl sm:rounded-3xl flex items-center justify-center">
              <MessageCircle size={32} className="text-primary" />
            </div>
            <h3 className="text-lg sm:text-xl font-semibold mb-2">Start your conversation</h3>
            <p className="text-muted-foreground text-sm sm:text-base">Ask me anything, and I'll help you out!</p>
          </div>
        ) : (
          processedMessages.map((message, index) => (
            <MessageBubble 
              key={message._id} 
              message={message} 
              messagePosition={index}
              currentBranchId={currentBranchId}
              isStreaming={isGenerating && index === processedMessages.length - 1 && message.role === "assistant"}
              onCopyMessage={handleCopyMessage}
              onEditMessage={handleEditMessage}
              onRetryMessage={handleRetryMessage}
              onBranchOff={handleBranchOff}
              onSwitchBranch={onSwitchBranch}
            />
          ))
        )}

        {/* Temporary loading message when AI is preparing to respond */}
        {isGenerating && (!processedMessages.length || processedMessages[processedMessages.length - 1]?.role !== "assistant") && (
          <div className="flex items-start gap-2 sm:gap-4">
            <div className="w-full space-y-3">
              <div className="px-4 sm:px-6 py-3 sm:py-4 bg-transparent rounded-3xl rounded-bl-lg border-none shadow-none">
                <LoadingDots size="md" />
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>
    </div>
  );
});
